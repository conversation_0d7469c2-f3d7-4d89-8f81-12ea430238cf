[{"D:\\code\\map\\my-map\\src\\main.js": "1", "D:\\code\\map\\my-map\\src\\App.vue": "2", "D:\\code\\map\\my-map\\src\\components\\Map3D.vue": "3", "D:\\code\\map\\my-map\\src\\classes\\Obstacle.js": "4", "D:\\code\\map\\my-map\\src\\managers\\DragManager.js": "5", "D:\\code\\map\\my-map\\src\\classes\\Person.js": "6", "D:\\code\\map\\my-map\\src\\utils\\threeUtils.js": "7"}, {"size": 90, "mtime": 1754297924642, "results": "8", "hashOfConfig": "9"}, {"size": 1035, "mtime": 1754360087529, "results": "10", "hashOfConfig": "9"}, {"size": 19043, "mtime": 1754377034917, "results": "11", "hashOfConfig": "9"}, {"size": 7726, "mtime": 1754360359140, "results": "12", "hashOfConfig": "9"}, {"size": 5999, "mtime": 1754360400919, "results": "13", "hashOfConfig": "9"}, {"size": 5127, "mtime": 1754374625314, "results": "14", "hashOfConfig": "9"}, {"size": 592, "mtime": 1754301273901, "results": "15", "hashOfConfig": "9"}, {"filePath": "16", "messages": "17", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "18"}, "13bxiin", {"filePath": "19", "messages": "20", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "21"}, {"filePath": "22", "messages": "23", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "24", "messages": "25", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "26", "messages": "27", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "18"}, {"filePath": "28", "messages": "29", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "18"}, {"filePath": "30", "messages": "31", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0, "usedDeprecatedRules": "18"}, "D:\\code\\map\\my-map\\src\\main.js", [], [], "D:\\code\\map\\my-map\\src\\App.vue", [], [], "D:\\code\\map\\my-map\\src\\components\\Map3D.vue", [], "D:\\code\\map\\my-map\\src\\classes\\Obstacle.js", [], "D:\\code\\map\\my-map\\src\\managers\\DragManager.js", [], "D:\\code\\map\\my-map\\src\\classes\\Person.js", [], "D:\\code\\map\\my-map\\src\\utils\\threeUtils.js", []]