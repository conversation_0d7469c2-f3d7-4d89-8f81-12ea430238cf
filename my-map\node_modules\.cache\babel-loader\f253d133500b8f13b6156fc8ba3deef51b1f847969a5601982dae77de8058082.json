{"ast": null, "code": "import * as THREE from 'three';\nexport class DragManager {\n  constructor(scene, camera, domElement) {\n    this.scene = scene;\n    this.camera = camera;\n    this.domElement = domElement;\n    this.raycaster = new THREE.Raycaster();\n    this.mouse = new THREE.Vector2();\n    this.isDragging = false;\n    this.dragObject = null;\n    this.dragPlane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0);\n    this.offset = new THREE.Vector3();\n    this.setupEventListeners();\n  }\n  setupEventListeners() {\n    this.domElement.addEventListener('mousedown', this.onMouseDown.bind(this));\n    this.domElement.addEventListener('mousemove', this.onMouseMove.bind(this));\n    this.domElement.addEventListener('mouseup', this.onMouseUp.bind(this));\n    this.domElement.addEventListener('contextmenu', this.onContextMenu.bind(this));\n  }\n  updateMousePosition(event) {\n    const rect = this.domElement.getBoundingClientRect();\n    this.mouse.x = (event.clientX - rect.left) / rect.width * 2 - 1;\n    this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;\n  }\n  onMouseDown(event) {\n    // 只处理左键点击\n    if (event.button !== 0) return;\n    this.updateMousePosition(event);\n    this.raycaster.setFromCamera(this.mouse, this.camera);\n\n    // 检查是否点击了可拖拽的对象\n    const intersects = this.raycaster.intersectObjects(this.scene.children, true);\n    for (let intersect of intersects) {\n      const object = intersect.object;\n\n      // 找到可拖拽的根对象（可能是父级Group）\n      const draggableObject = this.findDraggableParent(object);\n      if (draggableObject) {\n        this.isDragging = true;\n        this.dragObject = draggableObject;\n\n        // 计算拖拽偏移量\n        const intersectPoint = new THREE.Vector3();\n        this.raycaster.ray.intersectPlane(this.dragPlane, intersectPoint);\n\n        // 使用实际的mesh位置来计算偏移量，确保拖拽准确\n        const meshPosition = new THREE.Vector3();\n        draggableObject.getWorldPosition(meshPosition);\n        this.offset.copy(intersectPoint).sub(meshPosition);\n\n        // 禁用轨道控制器\n        if (this.controls) {\n          this.controls.enabled = false;\n        }\n\n        // 触发拖拽开始事件\n        this.onDragStart(draggableObject, intersect.point);\n        break;\n      }\n    }\n  }\n  onMouseMove(event) {\n    if (!this.isDragging || !this.dragObject) return;\n    this.updateMousePosition(event);\n    this.raycaster.setFromCamera(this.mouse, this.camera);\n\n    // 计算新位置\n    const intersectPoint = new THREE.Vector3();\n    this.raycaster.ray.intersectPlane(this.dragPlane, intersectPoint);\n    const newPosition = intersectPoint.sub(this.offset);\n\n    // 限制拖拽范围（可选）\n    newPosition.x = Math.max(-10, Math.min(10, newPosition.x));\n    newPosition.z = Math.max(-10, Math.min(10, newPosition.z));\n    newPosition.y = Math.max(0, newPosition.y);\n\n    // 更新对象位置\n    this.dragObject.position.copy(newPosition);\n\n    // 如果对象有自定义的位置更新逻辑，调用它\n    if (this.dragObject.userData && this.dragObject.userData.objectType) {\n      this.updateCustomObjectPosition(this.dragObject, newPosition);\n    }\n\n    // 触发拖拽中事件\n    this.onDragMove(this.dragObject, newPosition);\n  }\n  onMouseUp() {\n    if (this.isDragging && this.dragObject) {\n      // 触发拖拽结束事件\n      this.onDragEnd(this.dragObject, this.dragObject.position);\n      this.isDragging = false;\n      this.dragObject = null;\n\n      // 重新启用轨道控制器\n      if (this.controls) {\n        this.controls.enabled = true;\n      }\n    }\n  }\n  onContextMenu(event) {\n    event.preventDefault();\n  }\n\n  // 事件回调函数，可以被外部重写\n  onDragStart(object) {\n    // 可以在这里添加拖拽开始的视觉反馈\n    if (object.material) {\n      object.material.emissive.setHex(0x444444);\n    }\n  }\n  onDragMove(object) {\n    // 可以在这里添加拖拽过程中的逻辑\n    // 例如碰撞检测、吸附等\n    console.log('Dragging object:', object.userData?.objectType);\n  }\n  onDragEnd(object) {\n    // 可以在这里添加拖拽结束的逻辑\n    if (object.material) {\n      object.material.emissive.setHex(0x000000);\n    }\n  }\n\n  // 设置轨道控制器引用\n  setControls(controls) {\n    this.controls = controls;\n  }\n\n  // 添加可拖拽对象\n  makeDraggable(object) {\n    if (object.userData) {\n      object.userData.draggable = true;\n    } else {\n      object.userData = {\n        draggable: true\n      };\n    }\n  }\n\n  // 移除拖拽功能\n  makeNonDraggable(object) {\n    if (object.userData) {\n      object.userData.draggable = false;\n    }\n  }\n\n  // 检查对象是否可拖拽\n  isDraggableObject(object) {\n    return object.userData && object.userData.draggable === true;\n  }\n\n  // 查找可拖拽的父对象\n  findDraggableParent(object) {\n    let current = object;\n\n    // 向上遍历对象层次结构，寻找可拖拽的对象\n    while (current) {\n      if (current.userData && current.userData.draggable) {\n        return current;\n      }\n      current = current.parent;\n    }\n    return null;\n  }\n\n  // 更新自定义对象的位置\n  updateCustomObjectPosition(meshObject, newPosition) {\n    const objectType = meshObject.userData.objectType;\n    const objectId = meshObject.userData.objectId;\n\n    // 通过事件通知主应用更新对象位置\n    if (this.onObjectPositionUpdate) {\n      this.onObjectPositionUpdate(objectType, objectId, newPosition);\n    }\n  }\n\n  // 设置对象位置更新回调\n  setObjectPositionUpdateCallback(callback) {\n    this.onObjectPositionUpdate = callback;\n  }\n\n  // 销毁管理器\n  dispose() {\n    this.domElement.removeEventListener('mousedown', this.onMouseDown.bind(this));\n    this.domElement.removeEventListener('mousemove', this.onMouseMove.bind(this));\n    this.domElement.removeEventListener('mouseup', this.onMouseUp.bind(this));\n    this.domElement.removeEventListener('contextmenu', this.onContextMenu.bind(this));\n  }\n}", "map": {"version": 3, "names": ["THREE", "Drag<PERSON>anager", "constructor", "scene", "camera", "dom<PERSON>lement", "raycaster", "Raycaster", "mouse", "Vector2", "isDragging", "dragObject", "dragPlane", "Plane", "Vector3", "offset", "setupEventListeners", "addEventListener", "onMouseDown", "bind", "onMouseMove", "onMouseUp", "onContextMenu", "updateMousePosition", "event", "rect", "getBoundingClientRect", "x", "clientX", "left", "width", "y", "clientY", "top", "height", "button", "setFromCamera", "intersects", "intersectObjects", "children", "intersect", "object", "draggableObject", "findDraggableParent", "intersectPoint", "ray", "intersectPlane", "meshPosition", "getWorldPosition", "copy", "sub", "controls", "enabled", "onDragStart", "point", "newPosition", "Math", "max", "min", "z", "position", "userData", "objectType", "updateCustomObjectPosition", "onDragMove", "onDragEnd", "preventDefault", "material", "emissive", "setHex", "console", "log", "setControls", "makeDraggable", "draggable", "makeNonDraggable", "isDraggableObject", "current", "parent", "meshObject", "objectId", "onObjectPositionUpdate", "setObjectPositionUpdateCallback", "callback", "dispose", "removeEventListener"], "sources": ["D:/code/map/my-map/src/managers/DragManager.js"], "sourcesContent": ["import * as THREE from 'three'\n\nexport class DragManager {\n  constructor(scene, camera, domElement) {\n    this.scene = scene\n    this.camera = camera\n    this.domElement = domElement\n    this.raycaster = new THREE.Raycaster()\n    this.mouse = new THREE.Vector2()\n    this.isDragging = false\n    this.dragObject = null\n    this.dragPlane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0)\n    this.offset = new THREE.Vector3()\n    \n    this.setupEventListeners()\n  }\n  \n  setupEventListeners() {\n    this.domElement.addEventListener('mousedown', this.onMouseDown.bind(this))\n    this.domElement.addEventListener('mousemove', this.onMouseMove.bind(this))\n    this.domElement.addEventListener('mouseup', this.onMouseUp.bind(this))\n    this.domElement.addEventListener('contextmenu', this.onContextMenu.bind(this))\n  }\n  \n  updateMousePosition(event) {\n    const rect = this.domElement.getBoundingClientRect()\n    this.mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1\n    this.mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1\n  }\n  \n  onMouseDown(event) {\n    // 只处理左键点击\n    if (event.button !== 0) return\n\n    this.updateMousePosition(event)\n    this.raycaster.setFromCamera(this.mouse, this.camera)\n\n    // 检查是否点击了可拖拽的对象\n    const intersects = this.raycaster.intersectObjects(this.scene.children, true)\n\n    for (let intersect of intersects) {\n      const object = intersect.object\n\n      // 找到可拖拽的根对象（可能是父级Group）\n      const draggableObject = this.findDraggableParent(object)\n\n      if (draggableObject) {\n        this.isDragging = true\n        this.dragObject = draggableObject\n\n        // 计算拖拽偏移量\n        const intersectPoint = new THREE.Vector3()\n        this.raycaster.ray.intersectPlane(this.dragPlane, intersectPoint)\n\n        // 使用实际的mesh位置来计算偏移量，确保拖拽准确\n        const meshPosition = new THREE.Vector3()\n        draggableObject.getWorldPosition(meshPosition)\n        this.offset.copy(intersectPoint).sub(meshPosition)\n\n        // 禁用轨道控制器\n        if (this.controls) {\n          this.controls.enabled = false\n        }\n\n        // 触发拖拽开始事件\n        this.onDragStart(draggableObject, intersect.point)\n        break\n      }\n    }\n  }\n  \n  onMouseMove(event) {\n    if (!this.isDragging || !this.dragObject) return\n    \n    this.updateMousePosition(event)\n    this.raycaster.setFromCamera(this.mouse, this.camera)\n    \n    // 计算新位置\n    const intersectPoint = new THREE.Vector3()\n    this.raycaster.ray.intersectPlane(this.dragPlane, intersectPoint)\n    \n    const newPosition = intersectPoint.sub(this.offset)\n    \n    // 限制拖拽范围（可选）\n    newPosition.x = Math.max(-10, Math.min(10, newPosition.x))\n    newPosition.z = Math.max(-10, Math.min(10, newPosition.z))\n    newPosition.y = Math.max(0, newPosition.y)\n    \n    // 更新对象位置\n    this.dragObject.position.copy(newPosition)\n\n    // 如果对象有自定义的位置更新逻辑，调用它\n    if (this.dragObject.userData && this.dragObject.userData.objectType) {\n      this.updateCustomObjectPosition(this.dragObject, newPosition)\n    }\n\n    // 触发拖拽中事件\n    this.onDragMove(this.dragObject, newPosition)\n  }\n  \n  onMouseUp() {\n    if (this.isDragging && this.dragObject) {\n      // 触发拖拽结束事件\n      this.onDragEnd(this.dragObject, this.dragObject.position)\n\n      this.isDragging = false\n      this.dragObject = null\n\n      // 重新启用轨道控制器\n      if (this.controls) {\n        this.controls.enabled = true\n      }\n    }\n  }\n  \n  onContextMenu(event) {\n    event.preventDefault()\n  }\n  \n  // 事件回调函数，可以被外部重写\n  onDragStart(object) {\n    // 可以在这里添加拖拽开始的视觉反馈\n    if (object.material) {\n      object.material.emissive.setHex(0x444444)\n    }\n  }\n\n  onDragMove(object) {\n    // 可以在这里添加拖拽过程中的逻辑\n    // 例如碰撞检测、吸附等\n    console.log('Dragging object:', object.userData?.objectType)\n  }\n\n  onDragEnd(object) {\n    // 可以在这里添加拖拽结束的逻辑\n    if (object.material) {\n      object.material.emissive.setHex(0x000000)\n    }\n  }\n  \n  // 设置轨道控制器引用\n  setControls(controls) {\n    this.controls = controls\n  }\n  \n  // 添加可拖拽对象\n  makeDraggable(object) {\n    if (object.userData) {\n      object.userData.draggable = true\n    } else {\n      object.userData = { draggable: true }\n    }\n  }\n  \n  // 移除拖拽功能\n  makeNonDraggable(object) {\n    if (object.userData) {\n      object.userData.draggable = false\n    }\n  }\n  \n  // 检查对象是否可拖拽\n  isDraggableObject(object) {\n    return object.userData && object.userData.draggable === true\n  }\n\n  // 查找可拖拽的父对象\n  findDraggableParent(object) {\n    let current = object\n\n    // 向上遍历对象层次结构，寻找可拖拽的对象\n    while (current) {\n      if (current.userData && current.userData.draggable) {\n        return current\n      }\n      current = current.parent\n    }\n\n    return null\n  }\n\n  // 更新自定义对象的位置\n  updateCustomObjectPosition(meshObject, newPosition) {\n    const objectType = meshObject.userData.objectType\n    const objectId = meshObject.userData.objectId\n\n    // 通过事件通知主应用更新对象位置\n    if (this.onObjectPositionUpdate) {\n      this.onObjectPositionUpdate(objectType, objectId, newPosition)\n    }\n  }\n\n  // 设置对象位置更新回调\n  setObjectPositionUpdateCallback(callback) {\n    this.onObjectPositionUpdate = callback\n  }\n\n  // 销毁管理器\n  dispose() {\n    this.domElement.removeEventListener('mousedown', this.onMouseDown.bind(this))\n    this.domElement.removeEventListener('mousemove', this.onMouseMove.bind(this))\n    this.domElement.removeEventListener('mouseup', this.onMouseUp.bind(this))\n    this.domElement.removeEventListener('contextmenu', this.onContextMenu.bind(this))\n  }\n}\n"], "mappings": "AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAO,MAAMC,WAAW,CAAC;EACvBC,WAAWA,CAACC,KAAK,EAAEC,MAAM,EAAEC,UAAU,EAAE;IACrC,IAAI,CAACF,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,UAAU,GAAGA,UAAU;IAC5B,IAAI,CAACC,SAAS,GAAG,IAAIN,KAAK,CAACO,SAAS,CAAC,CAAC;IACtC,IAAI,CAACC,KAAK,GAAG,IAAIR,KAAK,CAACS,OAAO,CAAC,CAAC;IAChC,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,UAAU,GAAG,IAAI;IACtB,IAAI,CAACC,SAAS,GAAG,IAAIZ,KAAK,CAACa,KAAK,CAAC,IAAIb,KAAK,CAACc,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC,CAAC;IAC/D,IAAI,CAACC,MAAM,GAAG,IAAIf,KAAK,CAACc,OAAO,CAAC,CAAC;IAEjC,IAAI,CAACE,mBAAmB,CAAC,CAAC;EAC5B;EAEAA,mBAAmBA,CAAA,EAAG;IACpB,IAAI,CAACX,UAAU,CAACY,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACC,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1E,IAAI,CAACd,UAAU,CAACY,gBAAgB,CAAC,WAAW,EAAE,IAAI,CAACG,WAAW,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC;IAC1E,IAAI,CAACd,UAAU,CAACY,gBAAgB,CAAC,SAAS,EAAE,IAAI,CAACI,SAAS,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC;IACtE,IAAI,CAACd,UAAU,CAACY,gBAAgB,CAAC,aAAa,EAAE,IAAI,CAACK,aAAa,CAACH,IAAI,CAAC,IAAI,CAAC,CAAC;EAChF;EAEAI,mBAAmBA,CAACC,KAAK,EAAE;IACzB,MAAMC,IAAI,GAAG,IAAI,CAACpB,UAAU,CAACqB,qBAAqB,CAAC,CAAC;IACpD,IAAI,CAAClB,KAAK,CAACmB,CAAC,GAAI,CAACH,KAAK,CAACI,OAAO,GAAGH,IAAI,CAACI,IAAI,IAAIJ,IAAI,CAACK,KAAK,GAAI,CAAC,GAAG,CAAC;IACjE,IAAI,CAACtB,KAAK,CAACuB,CAAC,GAAG,EAAE,CAACP,KAAK,CAACQ,OAAO,GAAGP,IAAI,CAACQ,GAAG,IAAIR,IAAI,CAACS,MAAM,CAAC,GAAG,CAAC,GAAG,CAAC;EACpE;EAEAhB,WAAWA,CAACM,KAAK,EAAE;IACjB;IACA,IAAIA,KAAK,CAACW,MAAM,KAAK,CAAC,EAAE;IAExB,IAAI,CAACZ,mBAAmB,CAACC,KAAK,CAAC;IAC/B,IAAI,CAAClB,SAAS,CAAC8B,aAAa,CAAC,IAAI,CAAC5B,KAAK,EAAE,IAAI,CAACJ,MAAM,CAAC;;IAErD;IACA,MAAMiC,UAAU,GAAG,IAAI,CAAC/B,SAAS,CAACgC,gBAAgB,CAAC,IAAI,CAACnC,KAAK,CAACoC,QAAQ,EAAE,IAAI,CAAC;IAE7E,KAAK,IAAIC,SAAS,IAAIH,UAAU,EAAE;MAChC,MAAMI,MAAM,GAAGD,SAAS,CAACC,MAAM;;MAE/B;MACA,MAAMC,eAAe,GAAG,IAAI,CAACC,mBAAmB,CAACF,MAAM,CAAC;MAExD,IAAIC,eAAe,EAAE;QACnB,IAAI,CAAChC,UAAU,GAAG,IAAI;QACtB,IAAI,CAACC,UAAU,GAAG+B,eAAe;;QAEjC;QACA,MAAME,cAAc,GAAG,IAAI5C,KAAK,CAACc,OAAO,CAAC,CAAC;QAC1C,IAAI,CAACR,SAAS,CAACuC,GAAG,CAACC,cAAc,CAAC,IAAI,CAAClC,SAAS,EAAEgC,cAAc,CAAC;;QAEjE;QACA,MAAMG,YAAY,GAAG,IAAI/C,KAAK,CAACc,OAAO,CAAC,CAAC;QACxC4B,eAAe,CAACM,gBAAgB,CAACD,YAAY,CAAC;QAC9C,IAAI,CAAChC,MAAM,CAACkC,IAAI,CAACL,cAAc,CAAC,CAACM,GAAG,CAACH,YAAY,CAAC;;QAElD;QACA,IAAI,IAAI,CAACI,QAAQ,EAAE;UACjB,IAAI,CAACA,QAAQ,CAACC,OAAO,GAAG,KAAK;QAC/B;;QAEA;QACA,IAAI,CAACC,WAAW,CAACX,eAAe,EAAEF,SAAS,CAACc,KAAK,CAAC;QAClD;MACF;IACF;EACF;EAEAlC,WAAWA,CAACI,KAAK,EAAE;IACjB,IAAI,CAAC,IAAI,CAACd,UAAU,IAAI,CAAC,IAAI,CAACC,UAAU,EAAE;IAE1C,IAAI,CAACY,mBAAmB,CAACC,KAAK,CAAC;IAC/B,IAAI,CAAClB,SAAS,CAAC8B,aAAa,CAAC,IAAI,CAAC5B,KAAK,EAAE,IAAI,CAACJ,MAAM,CAAC;;IAErD;IACA,MAAMwC,cAAc,GAAG,IAAI5C,KAAK,CAACc,OAAO,CAAC,CAAC;IAC1C,IAAI,CAACR,SAAS,CAACuC,GAAG,CAACC,cAAc,CAAC,IAAI,CAAClC,SAAS,EAAEgC,cAAc,CAAC;IAEjE,MAAMW,WAAW,GAAGX,cAAc,CAACM,GAAG,CAAC,IAAI,CAACnC,MAAM,CAAC;;IAEnD;IACAwC,WAAW,CAAC5B,CAAC,GAAG6B,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEH,WAAW,CAAC5B,CAAC,CAAC,CAAC;IAC1D4B,WAAW,CAACI,CAAC,GAAGH,IAAI,CAACC,GAAG,CAAC,CAAC,EAAE,EAAED,IAAI,CAACE,GAAG,CAAC,EAAE,EAAEH,WAAW,CAACI,CAAC,CAAC,CAAC;IAC1DJ,WAAW,CAACxB,CAAC,GAAGyB,IAAI,CAACC,GAAG,CAAC,CAAC,EAAEF,WAAW,CAACxB,CAAC,CAAC;;IAE1C;IACA,IAAI,CAACpB,UAAU,CAACiD,QAAQ,CAACX,IAAI,CAACM,WAAW,CAAC;;IAE1C;IACA,IAAI,IAAI,CAAC5C,UAAU,CAACkD,QAAQ,IAAI,IAAI,CAAClD,UAAU,CAACkD,QAAQ,CAACC,UAAU,EAAE;MACnE,IAAI,CAACC,0BAA0B,CAAC,IAAI,CAACpD,UAAU,EAAE4C,WAAW,CAAC;IAC/D;;IAEA;IACA,IAAI,CAACS,UAAU,CAAC,IAAI,CAACrD,UAAU,EAAE4C,WAAW,CAAC;EAC/C;EAEAlC,SAASA,CAAA,EAAG;IACV,IAAI,IAAI,CAACX,UAAU,IAAI,IAAI,CAACC,UAAU,EAAE;MACtC;MACA,IAAI,CAACsD,SAAS,CAAC,IAAI,CAACtD,UAAU,EAAE,IAAI,CAACA,UAAU,CAACiD,QAAQ,CAAC;MAEzD,IAAI,CAAClD,UAAU,GAAG,KAAK;MACvB,IAAI,CAACC,UAAU,GAAG,IAAI;;MAEtB;MACA,IAAI,IAAI,CAACwC,QAAQ,EAAE;QACjB,IAAI,CAACA,QAAQ,CAACC,OAAO,GAAG,IAAI;MAC9B;IACF;EACF;EAEA9B,aAAaA,CAACE,KAAK,EAAE;IACnBA,KAAK,CAAC0C,cAAc,CAAC,CAAC;EACxB;;EAEA;EACAb,WAAWA,CAACZ,MAAM,EAAE;IAClB;IACA,IAAIA,MAAM,CAAC0B,QAAQ,EAAE;MACnB1B,MAAM,CAAC0B,QAAQ,CAACC,QAAQ,CAACC,MAAM,CAAC,QAAQ,CAAC;IAC3C;EACF;EAEAL,UAAUA,CAACvB,MAAM,EAAE;IACjB;IACA;IACA6B,OAAO,CAACC,GAAG,CAAC,kBAAkB,EAAE9B,MAAM,CAACoB,QAAQ,EAAEC,UAAU,CAAC;EAC9D;EAEAG,SAASA,CAACxB,MAAM,EAAE;IAChB;IACA,IAAIA,MAAM,CAAC0B,QAAQ,EAAE;MACnB1B,MAAM,CAAC0B,QAAQ,CAACC,QAAQ,CAACC,MAAM,CAAC,QAAQ,CAAC;IAC3C;EACF;;EAEA;EACAG,WAAWA,CAACrB,QAAQ,EAAE;IACpB,IAAI,CAACA,QAAQ,GAAGA,QAAQ;EAC1B;;EAEA;EACAsB,aAAaA,CAAChC,MAAM,EAAE;IACpB,IAAIA,MAAM,CAACoB,QAAQ,EAAE;MACnBpB,MAAM,CAACoB,QAAQ,CAACa,SAAS,GAAG,IAAI;IAClC,CAAC,MAAM;MACLjC,MAAM,CAACoB,QAAQ,GAAG;QAAEa,SAAS,EAAE;MAAK,CAAC;IACvC;EACF;;EAEA;EACAC,gBAAgBA,CAAClC,MAAM,EAAE;IACvB,IAAIA,MAAM,CAACoB,QAAQ,EAAE;MACnBpB,MAAM,CAACoB,QAAQ,CAACa,SAAS,GAAG,KAAK;IACnC;EACF;;EAEA;EACAE,iBAAiBA,CAACnC,MAAM,EAAE;IACxB,OAAOA,MAAM,CAACoB,QAAQ,IAAIpB,MAAM,CAACoB,QAAQ,CAACa,SAAS,KAAK,IAAI;EAC9D;;EAEA;EACA/B,mBAAmBA,CAACF,MAAM,EAAE;IAC1B,IAAIoC,OAAO,GAAGpC,MAAM;;IAEpB;IACA,OAAOoC,OAAO,EAAE;MACd,IAAIA,OAAO,CAAChB,QAAQ,IAAIgB,OAAO,CAAChB,QAAQ,CAACa,SAAS,EAAE;QAClD,OAAOG,OAAO;MAChB;MACAA,OAAO,GAAGA,OAAO,CAACC,MAAM;IAC1B;IAEA,OAAO,IAAI;EACb;;EAEA;EACAf,0BAA0BA,CAACgB,UAAU,EAAExB,WAAW,EAAE;IAClD,MAAMO,UAAU,GAAGiB,UAAU,CAAClB,QAAQ,CAACC,UAAU;IACjD,MAAMkB,QAAQ,GAAGD,UAAU,CAAClB,QAAQ,CAACmB,QAAQ;;IAE7C;IACA,IAAI,IAAI,CAACC,sBAAsB,EAAE;MAC/B,IAAI,CAACA,sBAAsB,CAACnB,UAAU,EAAEkB,QAAQ,EAAEzB,WAAW,CAAC;IAChE;EACF;;EAEA;EACA2B,+BAA+BA,CAACC,QAAQ,EAAE;IACxC,IAAI,CAACF,sBAAsB,GAAGE,QAAQ;EACxC;;EAEA;EACAC,OAAOA,CAAA,EAAG;IACR,IAAI,CAAC/E,UAAU,CAACgF,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACnE,WAAW,CAACC,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7E,IAAI,CAACd,UAAU,CAACgF,mBAAmB,CAAC,WAAW,EAAE,IAAI,CAACjE,WAAW,CAACD,IAAI,CAAC,IAAI,CAAC,CAAC;IAC7E,IAAI,CAACd,UAAU,CAACgF,mBAAmB,CAAC,SAAS,EAAE,IAAI,CAAChE,SAAS,CAACF,IAAI,CAAC,IAAI,CAAC,CAAC;IACzE,IAAI,CAACd,UAAU,CAACgF,mBAAmB,CAAC,aAAa,EAAE,IAAI,CAAC/D,aAAa,CAACH,IAAI,CAAC,IAAI,CAAC,CAAC;EACnF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}