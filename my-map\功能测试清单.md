# 功能测试清单

## 基本功能测试

### ✅ 对象创建
- [ ] 拖拽机器人到场景中
- [ ] 对象在正确位置创建
- [ ] 对象显示在场景对象列表中

### ✅ 对象选择
- [ ] 点击3D场景中的对象可以选中
- [ ] 点击场景对象列表中的项目可以选中
- [ ] 选中的对象会高亮显示
- [ ] 属性编辑面板显示正确信息

### ✅ 对象编辑
- [ ] 修改名称后立即生效
- [ ] 修改X、Y、Z坐标后对象移动到正确位置
- [ ] 修改朝向角度后对象转向正确方向

### ✅ 对象操作
- [ ] 复制对象功能正常
- [ ] 删除单个对象功能正常
- [ ] 清空场景功能正常

## 导出功能测试

### ✅ 导出场景
- [ ] 创建几个机器人对象
- [ ] 设置不同的位置和朝向
- [ ] 点击"导出场景"按钮
- [ ] 检查是否自动下载JSON文件
- [ ] 文件名格式是否正确（scene_YYYY-MM-DDTHH-mm-ss.json）

### ✅ 导出数据验证
- [ ] 打开导出的JSON文件
- [ ] 检查version字段是否为"1.0"
- [ ] 检查timestamp字段是否为有效时间戳
- [ ] 检查objects数组是否包含所有对象
- [ ] 检查每个对象的id、name、type、position、rotation字段
- [ ] 检查nextId字段是否正确

## 导入功能测试

### ✅ 导入示例场景
- [ ] 点击"导入场景"按钮
- [ ] 选择example-scene.json文件
- [ ] 确认替换当前场景
- [ ] 检查是否正确创建了3个机器人
- [ ] 检查机器人位置是否正确
- [ ] 检查机器人朝向是否正确

### ✅ 导入演示场景
- [ ] 点击"导入场景"按钮
- [ ] 选择demo-scene.json文件
- [ ] 确认替换当前场景
- [ ] 检查是否正确创建了6个机器人
- [ ] 检查所有机器人的名称、位置、朝向

### ✅ 导入验证
- [ ] 导入后的对象可以正常选择
- [ ] 导入后的对象可以正常编辑
- [ ] 导入后的对象可以正常删除
- [ ] nextId是否正确更新

## 完整流程测试

### ✅ 导出-导入循环测试
1. [ ] 创建一个复杂场景（多个对象，不同位置和朝向）
2. [ ] 导出场景为JSON文件
3. [ ] 清空当前场景
4. [ ] 导入刚才导出的JSON文件
5. [ ] 验证导入的场景与原场景完全一致

### ✅ 错误处理测试
- [ ] 导入无效JSON文件时显示错误提示
- [ ] 导入格式不正确的文件时显示错误提示
- [ ] 取消文件选择时不会出错

## 界面测试

### ✅ 按钮状态
- [ ] 没有对象时"复制对象"按钮不显示
- [ ] 没有对象时"清空场景"按钮不显示
- [ ] "导出场景"和"导入场景"按钮始终显示

### ✅ 样式测试
- [ ] 导出按钮为绿色
- [ ] 导入按钮为蓝色
- [ ] 清空场景按钮为红色
- [ ] 按钮悬停效果正常

## 性能测试

### ✅ 大量对象测试
- [ ] 创建10+个对象
- [ ] 导出功能正常
- [ ] 导入功能正常
- [ ] 场景渲染流畅

## 兼容性测试

### ✅ 浏览器兼容性
- [ ] Chrome浏览器正常
- [ ] Firefox浏览器正常
- [ ] Edge浏览器正常

### ✅ 文件系统兼容性
- [ ] Windows系统下载正常
- [ ] 文件名不包含非法字符
- [ ] 文件编码为UTF-8

## 测试结果

测试日期：_______
测试人员：_______
测试环境：_______

通过的测试项：_____ / _____
失败的测试项：_____

### 发现的问题
1. 
2. 
3. 

### 建议改进
1. 
2. 
3. 
