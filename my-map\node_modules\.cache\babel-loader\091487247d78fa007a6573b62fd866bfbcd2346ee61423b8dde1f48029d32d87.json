{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport * as THREE from 'three';\nexport class Person {\n  constructor(id, name, position = new THREE.Vector3(0, 0, 0)) {\n    this.id = id;\n    this.name = name;\n    this.type = 'person';\n    this.position = position.clone();\n    this.isSelected = false;\n    this.rotation = 0; // 人物朝向角度（弧度），0表示面向+Z方向（正面朝前）\n\n    this.createMesh();\n    this.setupAnimation();\n  }\n  createMesh() {\n    // 创建人物的3D模型（简化版，使用基本几何体）\n    const group = new THREE.Group();\n\n    // 身体（圆柱体）\n    const bodyGeometry = new THREE.CylinderGeometry(0.3, 0.4, 1.2, 8);\n    const bodyMaterial = new THREE.MeshLambertMaterial({\n      color: 0x4a90e2\n    });\n    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);\n    body.position.y = 0.6;\n    body.castShadow = true;\n    group.add(body);\n\n    // 头部（球体）\n    const headGeometry = new THREE.SphereGeometry(0.25, 8, 6);\n    const headMaterial = new THREE.MeshLambertMaterial({\n      color: 0xffdbac\n    });\n    const head = new THREE.Mesh(headGeometry, headMaterial);\n    head.position.y = 1.45;\n    head.castShadow = true;\n    group.add(head);\n\n    // 添加脸部特征（眼睛和鼻子）来区分正面和背面\n    // 眼睛（两个小球体）\n    const eyeGeometry = new THREE.SphereGeometry(0.03, 4, 4);\n    const eyeMaterial = new THREE.MeshLambertMaterial({\n      color: 0x000000\n    });\n    const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial);\n    leftEye.position.set(-0.08, 1.5, 0.2); // 左眼，Z=0.2表示在头部前方\n    group.add(leftEye);\n    const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial);\n    rightEye.position.set(0.08, 1.5, 0.2); // 右眼\n    group.add(rightEye);\n\n    // 鼻子（小圆锥体）\n    const noseGeometry = new THREE.ConeGeometry(0.02, 0.06, 4);\n    const noseMaterial = new THREE.MeshLambertMaterial({\n      color: 0xffdbac\n    });\n    const nose = new THREE.Mesh(noseGeometry, noseMaterial);\n    nose.position.set(0, 1.42, 0.22); // 鼻子在脸部前方\n    nose.rotation.x = Math.PI / 2; // 让鼻子朝前\n    group.add(nose);\n\n    // 左臂\n    const armGeometry = new THREE.CylinderGeometry(0.08, 0.08, 0.8, 6);\n    const armMaterial = new THREE.MeshLambertMaterial({\n      color: 0xffdbac\n    });\n    const leftArm = new THREE.Mesh(armGeometry, armMaterial);\n    leftArm.position.set(-0.45, 0.8, 0);\n    leftArm.rotation.z = Math.PI / 6;\n    leftArm.castShadow = true;\n    group.add(leftArm);\n    const rightArm = new THREE.Mesh(armGeometry, armMaterial);\n    rightArm.position.set(0.45, 0.8, 0);\n    rightArm.rotation.z = -Math.PI / 6;\n    rightArm.castShadow = true;\n    group.add(rightArm);\n\n    // 左腿\n    const legGeometry = new THREE.CylinderGeometry(0.1, 0.1, 0.8, 6);\n    const legMaterial = new THREE.MeshLambertMaterial({\n      color: 0x2c3e50\n    });\n    const leftLeg = new THREE.Mesh(legGeometry, legMaterial);\n    leftLeg.position.set(-0.15, -0.4, 0);\n    leftLeg.castShadow = true;\n    group.add(leftLeg);\n    const rightLeg = new THREE.Mesh(legGeometry, legMaterial);\n    rightLeg.position.set(0.15, -0.4, 0);\n    rightLeg.castShadow = true;\n    group.add(rightLeg);\n\n    // 设置位置\n    group.position.copy(this.position);\n\n    // 添加用户数据\n    group.userData = {\n      draggable: true,\n      objectType: 'person',\n      objectId: this.id\n    };\n    this.mesh = group;\n    this.bodyParts = {\n      body,\n      head,\n      leftArm,\n      rightArm,\n      leftLeg,\n      rightLeg\n    };\n  }\n  setupAnimation() {\n    this.animationTime = 0;\n    this.walkCycle = {\n      armSwing: 0,\n      legSwing: 0\n    };\n  }\n  update(deltaTime = 0.016) {\n    this.animationTime += deltaTime;\n\n    // 更新网格位置\n    if (this.mesh) {\n      this.mesh.position.copy(this.position);\n    }\n  }\n  setSelected(selected) {\n    this.isSelected = selected;\n    if (this.mesh) {\n      this.mesh.children.forEach(child => {\n        if (child.material) {\n          if (selected) {\n            child.material.emissive.setHex(0x444444);\n          } else {\n            child.material.emissive.setHex(0x000000);\n          }\n        }\n      });\n    }\n  }\n  updateProperty(property, value) {\n    switch (property) {\n      case 'name':\n        this.name = value;\n        break;\n    }\n  }\n\n  // 获取边界框（用于碰撞检测）\n  getBoundingBox() {\n    const box = new THREE.Box3();\n    if (this.mesh) {\n      box.setFromObject(this.mesh);\n    }\n    return box;\n  }\n\n  // 销毁对象\n  dispose() {\n    if (this.mesh) {\n      this.mesh.children.forEach(child => {\n        if (child.geometry) child.geometry.dispose();\n        if (child.material) child.material.dispose();\n      });\n    }\n  }\n}", "map": {"version": 3, "names": ["THREE", "Person", "constructor", "id", "name", "position", "Vector3", "type", "clone", "isSelected", "rotation", "<PERSON><PERSON><PERSON>", "setupAnimation", "group", "Group", "bodyGeometry", "CylinderGeometry", "bodyMaterial", "MeshLambertMaterial", "color", "body", "<PERSON><PERSON>", "y", "<PERSON><PERSON><PERSON><PERSON>", "add", "headGeometry", "SphereGeometry", "headMaterial", "head", "eyeGeometry", "eyeMaterial", "leftEye", "set", "rightEye", "noseGeometry", "ConeGeometry", "noseMaterial", "nose", "x", "Math", "PI", "armGeometry", "armMaterial", "leftArm", "z", "rightArm", "legGeometry", "legMaterial", "leftLeg", "rightLeg", "copy", "userData", "draggable", "objectType", "objectId", "mesh", "bodyParts", "animationTime", "walkCycle", "armSwing", "legSwing", "update", "deltaTime", "setSelected", "selected", "children", "for<PERSON>ach", "child", "material", "emissive", "setHex", "updateProperty", "property", "value", "getBoundingBox", "box", "Box3", "setFromObject", "dispose", "geometry"], "sources": ["D:/code/map/my-map/src/classes/Person.js"], "sourcesContent": ["import * as THREE from 'three'\n\nexport class Person {\n  constructor(id, name, position = new THREE.Vector3(0, 0, 0)) {\n    this.id = id\n    this.name = name\n    this.type = 'person'\n    this.position = position.clone()\n    this.isSelected = false\n    this.rotation = 0 // 人物朝向角度（弧度），0表示面向+Z<PERSON>向（正面朝前）\n\n    this.createMesh()\n    this.setupAnimation()\n  }\n  \n  createMesh() {\n    // 创建人物的3D模型（简化版，使用基本几何体）\n    const group = new THREE.Group()\n\n    // 身体（圆柱体）\n    const bodyGeometry = new THREE.CylinderGeometry(0.3, 0.4, 1.2, 8)\n    const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x4a90e2 })\n    const body = new THREE.Mesh(bodyGeometry, bodyMaterial)\n    body.position.y = 0.6\n    body.castShadow = true\n    group.add(body)\n    \n    // 头部（球体）\n    const headGeometry = new THREE.SphereGeometry(0.25, 8, 6)\n    const headMaterial = new THREE.MeshLambertMaterial({ color: 0xffdbac })\n    const head = new THREE.Mesh(headGeometry, headMaterial)\n    head.position.y = 1.45\n    head.castShadow = true\n    group.add(head)\n\n    // 添加脸部特征（眼睛和鼻子）来区分正面和背面\n    // 眼睛（两个小球体）\n    const eyeGeometry = new THREE.SphereGeometry(0.03, 4, 4)\n    const eyeMaterial = new THREE.MeshLambertMaterial({ color: 0x000000 })\n\n    const leftEye = new THREE.Mesh(eyeGeometry, eyeMaterial)\n    leftEye.position.set(-0.08, 1.5, 0.2) // 左眼，Z=0.2表示在头部前方\n    group.add(leftEye)\n\n    const rightEye = new THREE.Mesh(eyeGeometry, eyeMaterial)\n    rightEye.position.set(0.08, 1.5, 0.2) // 右眼\n    group.add(rightEye)\n\n    // 鼻子（小圆锥体）\n    const noseGeometry = new THREE.ConeGeometry(0.02, 0.06, 4)\n    const noseMaterial = new THREE.MeshLambertMaterial({ color: 0xffdbac })\n    const nose = new THREE.Mesh(noseGeometry, noseMaterial)\n    nose.position.set(0, 1.42, 0.22) // 鼻子在脸部前方\n    nose.rotation.x = Math.PI / 2 // 让鼻子朝前\n    group.add(nose)\n    \n    // 左臂\n    const armGeometry = new THREE.CylinderGeometry(0.08, 0.08, 0.8, 6)\n    const armMaterial = new THREE.MeshLambertMaterial({ color: 0xffdbac })\n    \n    const leftArm = new THREE.Mesh(armGeometry, armMaterial)\n    leftArm.position.set(-0.45, 0.8, 0)\n    leftArm.rotation.z = Math.PI / 6\n    leftArm.castShadow = true\n    group.add(leftArm)\n    \n    const rightArm = new THREE.Mesh(armGeometry, armMaterial)\n    rightArm.position.set(0.45, 0.8, 0)\n    rightArm.rotation.z = -Math.PI / 6\n    rightArm.castShadow = true\n    group.add(rightArm)\n    \n    // 左腿\n    const legGeometry = new THREE.CylinderGeometry(0.1, 0.1, 0.8, 6)\n    const legMaterial = new THREE.MeshLambertMaterial({ color: 0x2c3e50 })\n    \n    const leftLeg = new THREE.Mesh(legGeometry, legMaterial)\n    leftLeg.position.set(-0.15, -0.4, 0)\n    leftLeg.castShadow = true\n    group.add(leftLeg)\n    \n    const rightLeg = new THREE.Mesh(legGeometry, legMaterial)\n    rightLeg.position.set(0.15, -0.4, 0)\n    rightLeg.castShadow = true\n    group.add(rightLeg)\n    \n    // 设置位置\n    group.position.copy(this.position)\n    \n    // 添加用户数据\n    group.userData = {\n      draggable: true,\n      objectType: 'person',\n      objectId: this.id\n    }\n    \n    this.mesh = group\n    this.bodyParts = {\n      body,\n      head,\n      leftArm,\n      rightArm,\n      leftLeg,\n      rightLeg\n    }\n  }\n  \n  setupAnimation() {\n    this.animationTime = 0\n    this.walkCycle = {\n      armSwing: 0,\n      legSwing: 0\n    }\n  }\n  \n  update(deltaTime = 0.016) {\n    this.animationTime += deltaTime\n\n    // 更新网格位置\n    if (this.mesh) {\n      this.mesh.position.copy(this.position)\n    }\n  }\n  \n\n  \n  setSelected(selected) {\n    this.isSelected = selected\n    \n    if (this.mesh) {\n      this.mesh.children.forEach(child => {\n        if (child.material) {\n          if (selected) {\n            child.material.emissive.setHex(0x444444)\n          } else {\n            child.material.emissive.setHex(0x000000)\n          }\n        }\n      })\n    }\n  }\n  \n  updateProperty(property, value) {\n    switch (property) {\n      case 'name':\n        this.name = value\n        break\n    }\n  }\n  \n  // 获取边界框（用于碰撞检测）\n  getBoundingBox() {\n    const box = new THREE.Box3()\n    if (this.mesh) {\n      box.setFromObject(this.mesh)\n    }\n    return box\n  }\n  \n  // 销毁对象\n  dispose() {\n    if (this.mesh) {\n      this.mesh.children.forEach(child => {\n        if (child.geometry) child.geometry.dispose()\n        if (child.material) child.material.dispose()\n      })\n    }\n  }\n}\n"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAO,MAAMC,MAAM,CAAC;EAClBC,WAAWA,CAACC,EAAE,EAAEC,IAAI,EAAEC,QAAQ,GAAG,IAAIL,KAAK,CAACM,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;IAC3D,IAAI,CAACH,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,IAAI,GAAG,QAAQ;IACpB,IAAI,CAACF,QAAQ,GAAGA,QAAQ,CAACG,KAAK,CAAC,CAAC;IAChC,IAAI,CAACC,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,QAAQ,GAAG,CAAC,EAAC;;IAElB,IAAI,CAACC,UAAU,CAAC,CAAC;IACjB,IAAI,CAACC,cAAc,CAAC,CAAC;EACvB;EAEAD,UAAUA,CAAA,EAAG;IACX;IACA,MAAME,KAAK,GAAG,IAAIb,KAAK,CAACc,KAAK,CAAC,CAAC;;IAE/B;IACA,MAAMC,YAAY,GAAG,IAAIf,KAAK,CAACgB,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACjE,MAAMC,YAAY,GAAG,IAAIjB,KAAK,CAACkB,mBAAmB,CAAC;MAAEC,KAAK,EAAE;IAAS,CAAC,CAAC;IACvE,MAAMC,IAAI,GAAG,IAAIpB,KAAK,CAACqB,IAAI,CAACN,YAAY,EAAEE,YAAY,CAAC;IACvDG,IAAI,CAACf,QAAQ,CAACiB,CAAC,GAAG,GAAG;IACrBF,IAAI,CAACG,UAAU,GAAG,IAAI;IACtBV,KAAK,CAACW,GAAG,CAACJ,IAAI,CAAC;;IAEf;IACA,MAAMK,YAAY,GAAG,IAAIzB,KAAK,CAAC0B,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;IACzD,MAAMC,YAAY,GAAG,IAAI3B,KAAK,CAACkB,mBAAmB,CAAC;MAAEC,KAAK,EAAE;IAAS,CAAC,CAAC;IACvE,MAAMS,IAAI,GAAG,IAAI5B,KAAK,CAACqB,IAAI,CAACI,YAAY,EAAEE,YAAY,CAAC;IACvDC,IAAI,CAACvB,QAAQ,CAACiB,CAAC,GAAG,IAAI;IACtBM,IAAI,CAACL,UAAU,GAAG,IAAI;IACtBV,KAAK,CAACW,GAAG,CAACI,IAAI,CAAC;;IAEf;IACA;IACA,MAAMC,WAAW,GAAG,IAAI7B,KAAK,CAAC0B,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;IACxD,MAAMI,WAAW,GAAG,IAAI9B,KAAK,CAACkB,mBAAmB,CAAC;MAAEC,KAAK,EAAE;IAAS,CAAC,CAAC;IAEtE,MAAMY,OAAO,GAAG,IAAI/B,KAAK,CAACqB,IAAI,CAACQ,WAAW,EAAEC,WAAW,CAAC;IACxDC,OAAO,CAAC1B,QAAQ,CAAC2B,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,EAAC;IACtCnB,KAAK,CAACW,GAAG,CAACO,OAAO,CAAC;IAElB,MAAME,QAAQ,GAAG,IAAIjC,KAAK,CAACqB,IAAI,CAACQ,WAAW,EAAEC,WAAW,CAAC;IACzDG,QAAQ,CAAC5B,QAAQ,CAAC2B,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,GAAG,CAAC,EAAC;IACtCnB,KAAK,CAACW,GAAG,CAACS,QAAQ,CAAC;;IAEnB;IACA,MAAMC,YAAY,GAAG,IAAIlC,KAAK,CAACmC,YAAY,CAAC,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC;IAC1D,MAAMC,YAAY,GAAG,IAAIpC,KAAK,CAACkB,mBAAmB,CAAC;MAAEC,KAAK,EAAE;IAAS,CAAC,CAAC;IACvE,MAAMkB,IAAI,GAAG,IAAIrC,KAAK,CAACqB,IAAI,CAACa,YAAY,EAAEE,YAAY,CAAC;IACvDC,IAAI,CAAChC,QAAQ,CAAC2B,GAAG,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,CAAC,EAAC;IACjCK,IAAI,CAAC3B,QAAQ,CAAC4B,CAAC,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC,EAAC;IAC9B3B,KAAK,CAACW,GAAG,CAACa,IAAI,CAAC;;IAEf;IACA,MAAMI,WAAW,GAAG,IAAIzC,KAAK,CAACgB,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;IAClE,MAAM0B,WAAW,GAAG,IAAI1C,KAAK,CAACkB,mBAAmB,CAAC;MAAEC,KAAK,EAAE;IAAS,CAAC,CAAC;IAEtE,MAAMwB,OAAO,GAAG,IAAI3C,KAAK,CAACqB,IAAI,CAACoB,WAAW,EAAEC,WAAW,CAAC;IACxDC,OAAO,CAACtC,QAAQ,CAAC2B,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;IACnCW,OAAO,CAACjC,QAAQ,CAACkC,CAAC,GAAGL,IAAI,CAACC,EAAE,GAAG,CAAC;IAChCG,OAAO,CAACpB,UAAU,GAAG,IAAI;IACzBV,KAAK,CAACW,GAAG,CAACmB,OAAO,CAAC;IAElB,MAAME,QAAQ,GAAG,IAAI7C,KAAK,CAACqB,IAAI,CAACoB,WAAW,EAAEC,WAAW,CAAC;IACzDG,QAAQ,CAACxC,QAAQ,CAAC2B,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;IACnCa,QAAQ,CAACnC,QAAQ,CAACkC,CAAC,GAAG,CAACL,IAAI,CAACC,EAAE,GAAG,CAAC;IAClCK,QAAQ,CAACtB,UAAU,GAAG,IAAI;IAC1BV,KAAK,CAACW,GAAG,CAACqB,QAAQ,CAAC;;IAEnB;IACA,MAAMC,WAAW,GAAG,IAAI9C,KAAK,CAACgB,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAChE,MAAM+B,WAAW,GAAG,IAAI/C,KAAK,CAACkB,mBAAmB,CAAC;MAAEC,KAAK,EAAE;IAAS,CAAC,CAAC;IAEtE,MAAM6B,OAAO,GAAG,IAAIhD,KAAK,CAACqB,IAAI,CAACyB,WAAW,EAAEC,WAAW,CAAC;IACxDC,OAAO,CAAC3C,QAAQ,CAAC2B,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IACpCgB,OAAO,CAACzB,UAAU,GAAG,IAAI;IACzBV,KAAK,CAACW,GAAG,CAACwB,OAAO,CAAC;IAElB,MAAMC,QAAQ,GAAG,IAAIjD,KAAK,CAACqB,IAAI,CAACyB,WAAW,EAAEC,WAAW,CAAC;IACzDE,QAAQ,CAAC5C,QAAQ,CAAC2B,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IACpCiB,QAAQ,CAAC1B,UAAU,GAAG,IAAI;IAC1BV,KAAK,CAACW,GAAG,CAACyB,QAAQ,CAAC;;IAEnB;IACApC,KAAK,CAACR,QAAQ,CAAC6C,IAAI,CAAC,IAAI,CAAC7C,QAAQ,CAAC;;IAElC;IACAQ,KAAK,CAACsC,QAAQ,GAAG;MACfC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,QAAQ;MACpBC,QAAQ,EAAE,IAAI,CAACnD;IACjB,CAAC;IAED,IAAI,CAACoD,IAAI,GAAG1C,KAAK;IACjB,IAAI,CAAC2C,SAAS,GAAG;MACfpC,IAAI;MACJQ,IAAI;MACJe,OAAO;MACPE,QAAQ;MACRG,OAAO;MACPC;IACF,CAAC;EACH;EAEArC,cAAcA,CAAA,EAAG;IACf,IAAI,CAAC6C,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,SAAS,GAAG;MACfC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE;IACZ,CAAC;EACH;EAEAC,MAAMA,CAACC,SAAS,GAAG,KAAK,EAAE;IACxB,IAAI,CAACL,aAAa,IAAIK,SAAS;;IAE/B;IACA,IAAI,IAAI,CAACP,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAAClD,QAAQ,CAAC6C,IAAI,CAAC,IAAI,CAAC7C,QAAQ,CAAC;IACxC;EACF;EAIA0D,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAI,CAACvD,UAAU,GAAGuD,QAAQ;IAE1B,IAAI,IAAI,CAACT,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAACU,QAAQ,CAACC,OAAO,CAACC,KAAK,IAAI;QAClC,IAAIA,KAAK,CAACC,QAAQ,EAAE;UAClB,IAAIJ,QAAQ,EAAE;YACZG,KAAK,CAACC,QAAQ,CAACC,QAAQ,CAACC,MAAM,CAAC,QAAQ,CAAC;UAC1C,CAAC,MAAM;YACLH,KAAK,CAACC,QAAQ,CAACC,QAAQ,CAACC,MAAM,CAAC,QAAQ,CAAC;UAC1C;QACF;MACF,CAAC,CAAC;IACJ;EACF;EAEAC,cAAcA,CAACC,QAAQ,EAAEC,KAAK,EAAE;IAC9B,QAAQD,QAAQ;MACd,KAAK,MAAM;QACT,IAAI,CAACpE,IAAI,GAAGqE,KAAK;QACjB;IACJ;EACF;;EAEA;EACAC,cAAcA,CAAA,EAAG;IACf,MAAMC,GAAG,GAAG,IAAI3E,KAAK,CAAC4E,IAAI,CAAC,CAAC;IAC5B,IAAI,IAAI,CAACrB,IAAI,EAAE;MACboB,GAAG,CAACE,aAAa,CAAC,IAAI,CAACtB,IAAI,CAAC;IAC9B;IACA,OAAOoB,GAAG;EACZ;;EAEA;EACAG,OAAOA,CAAA,EAAG;IACR,IAAI,IAAI,CAACvB,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAACU,QAAQ,CAACC,OAAO,CAACC,KAAK,IAAI;QAClC,IAAIA,KAAK,CAACY,QAAQ,EAAEZ,KAAK,CAACY,QAAQ,CAACD,OAAO,CAAC,CAAC;QAC5C,IAAIX,KAAK,CAACC,QAAQ,EAAED,KAAK,CAACC,QAAQ,CAACU,OAAO,CAAC,CAAC;MAC9C,CAAC,CAAC;IACJ;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}