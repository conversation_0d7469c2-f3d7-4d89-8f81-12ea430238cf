{"ast": null, "code": "import Map3D from './components/Map3D.vue';\nexport default {\n  name: 'App',\n  components: {\n    Map3D\n  }\n};", "map": {"version": 3, "names": ["Map3D", "name", "components"], "sources": ["D:\\code\\map\\my-map\\src\\App.vue"], "sourcesContent": ["<template>\n  <div id=\"app\">\n    <!-- <header class=\"app-header\">\n      <h1>室内3D地图路线系统</h1>\n      <p>拖拽左侧元素到3D场景中，创建人物、路线和障碍物</p>\n    </header> -->\n    <Map3D />\n  </div>\n</template>\n\n<script>\nimport Map3D from './components/Map3D.vue'\n\nexport default {\n  name: 'App',\n  components: {\n    Map3D\n  }\n}\n</script>\n\n<style>\n* {\n  margin: 0;\n  padding: 0;\n  box-sizing: border-box;\n}\n\n#app {\n  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;\n  -webkit-font-smoothing: antialiased;\n  -moz-osx-font-smoothing: grayscale;\n  height: 100vh;\n  display: flex;\n  flex-direction: column;\n}\n\n.app-header {\n  background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);\n  color: white;\n  padding: 20px;\n  text-align: center;\n  box-shadow: 0 2px 10px rgba(0,0,0,0.1);\n}\n\n.app-header h1 {\n  font-size: 28px;\n  margin-bottom: 8px;\n  font-weight: 600;\n}\n\n.app-header p {\n  font-size: 16px;\n  opacity: 0.9;\n  font-weight: 300;\n}\n\nbody {\n  margin: 0;\n  overflow: hidden;\n}\n</style>\n"], "mappings": "AAWA,OAAOA,KAAI,MAAO,wBAAuB;AAEzC,eAAe;EACbC,IAAI,EAAE,KAAK;EACXC,UAAU,EAAE;IACVF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}