{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport * as THREE from 'three';\nexport class Obstacle {\n  constructor(id, name, position = new THREE.Vector3(0, 0, 0), width = 1, height = 1, depth = 1) {\n    this.id = id;\n    this.name = name;\n    this.type = 'obstacle';\n    this.position = position.clone();\n    this.width = width;\n    this.height = height;\n    this.depth = depth;\n    this.isSelected = false;\n    this.color = 0x8b4513; // 棕色\n\n    this.createMesh();\n  }\n  createMesh() {\n    // 创建长方体几何体\n    const geometry = new THREE.BoxGeometry(this.width, this.height, this.depth);\n\n    // 创建材质\n    const material = new THREE.MeshLambertMaterial({\n      color: this.color,\n      transparent: true,\n      opacity: 0.8\n    });\n\n    // 创建网格\n    const meshObject = new THREE.Mesh(geometry, material);\n    meshObject.position.y = this.height / 2; // 让底部贴地\n\n    // 启用阴影\n    meshObject.castShadow = true;\n    meshObject.receiveShadow = true;\n\n    // 创建边框线条（选中时显示）\n    const wireframeGeometry = new THREE.EdgesGeometry(geometry);\n    const wireframeMaterial = new THREE.LineBasicMaterial({\n      color: 0x000000,\n      transparent: true,\n      opacity: 0.3\n    });\n    this.wireframe = new THREE.LineSegments(wireframeGeometry, wireframeMaterial);\n    this.wireframe.position.y = this.height / 2;\n    this.wireframe.visible = false;\n\n    // 创建一个组来包含网格和线框\n    this.group = new THREE.Group();\n    this.group.add(meshObject);\n    this.group.add(this.wireframe);\n\n    // 设置组的位置为对象位置\n    this.group.position.copy(this.position);\n\n    // 设置用户数据在组上\n    this.group.userData = {\n      draggable: true,\n      objectType: 'obstacle',\n      objectId: this.id\n    };\n\n    // 主要引用指向组\n    this.mesh = this.group;\n  }\n  updateGeometry() {\n    // 移除旧的几何体\n    const oldMesh = this.group.children[0];\n    const oldWireframe = this.group.children[1];\n    this.group.remove(oldMesh);\n    this.group.remove(oldWireframe);\n    oldMesh.geometry.dispose();\n    oldMesh.material.dispose();\n    oldWireframe.geometry.dispose();\n    oldWireframe.material.dispose();\n\n    // 创建新的几何体\n    const geometry = new THREE.BoxGeometry(this.width, this.height, this.depth);\n    const material = new THREE.MeshLambertMaterial({\n      color: this.color,\n      transparent: true,\n      opacity: 0.8\n    });\n    const newMesh = new THREE.Mesh(geometry, material);\n    newMesh.position.y = this.height / 2; // 只设置Y偏移，让底部贴地\n    newMesh.castShadow = true;\n    newMesh.receiveShadow = true;\n\n    // 创建新的线框\n    const wireframeGeometry = new THREE.EdgesGeometry(geometry);\n    const wireframeMaterial = new THREE.LineBasicMaterial({\n      color: 0x000000,\n      transparent: true,\n      opacity: 0.3\n    });\n    const newWireframe = new THREE.LineSegments(wireframeGeometry, wireframeMaterial);\n    newWireframe.position.y = this.height / 2;\n    newWireframe.visible = this.isSelected;\n    this.group.add(newMesh);\n    this.group.add(newWireframe);\n    this.wireframe = newWireframe;\n  }\n  setSelected(selected) {\n    this.isSelected = selected;\n    if (this.wireframe) {\n      this.wireframe.visible = selected;\n    }\n\n    // 高亮效果\n    const meshObject = this.group.children[0];\n    if (meshObject && meshObject.material) {\n      if (selected) {\n        meshObject.material.emissive.setHex(0x444444);\n        meshObject.material.opacity = 1.0;\n      } else {\n        meshObject.material.emissive.setHex(0x000000);\n        meshObject.material.opacity = 0.8;\n      }\n    }\n  }\n  updateProperty(property, value) {\n    switch (property) {\n      case 'name':\n        this.name = value;\n        break;\n      case 'width':\n        this.width = Math.max(0.1, value);\n        this.updateGeometry();\n        break;\n      case 'height':\n        this.height = Math.max(0.1, value);\n        this.updateGeometry();\n        break;\n      case 'depth':\n        this.depth = Math.max(0.1, value);\n        this.updateGeometry();\n        break;\n      case 'color':\n        {\n          this.color = value;\n          const meshObject = this.group.children[0];\n          if (meshObject && meshObject.material) {\n            meshObject.material.color.setHex(value);\n          }\n          break;\n        }\n    }\n  }\n\n  // 更新位置\n  updatePosition(newPosition) {\n    this.position.copy(newPosition);\n    if (this.group) {\n      this.group.position.copy(newPosition);\n\n      // 更新内部网格的Y位置以保持底部贴地\n      const meshObject = this.group.children[0];\n      const wireframe = this.group.children[1];\n      if (meshObject) {\n        meshObject.position.y = this.height / 2;\n      }\n      if (wireframe) {\n        wireframe.position.y = this.height / 2;\n      }\n    }\n  }\n\n  // 获取边界框（用于碰撞检测）\n  getBoundingBox() {\n    const box = new THREE.Box3();\n    if (this.mesh) {\n      box.setFromObject(this.mesh);\n    }\n    return box;\n  }\n\n  // 检查点是否在障碍物内部\n  containsPoint(point) {\n    const box = this.getBoundingBox();\n    return box.containsPoint(point);\n  }\n\n  // 检查与另一个边界框是否相交\n  intersectsBox(otherBox) {\n    const box = this.getBoundingBox();\n    return box.intersectsBox(otherBox);\n  }\n\n  // 检查与球体是否相交（用于人物碰撞检测）\n  intersectsSphere(center, radius) {\n    const box = this.getBoundingBox();\n    const sphere = new THREE.Sphere(center, radius);\n    return box.intersectsSphere(sphere);\n  }\n\n  // 获取最近的表面点（用于路径规划）\n  getClosestSurfacePoint(point) {\n    const box = this.getBoundingBox();\n    const closestPoint = new THREE.Vector3();\n\n    // 将点限制在边界框内\n    closestPoint.copy(point);\n    closestPoint.clamp(box.min, box.max);\n    return closestPoint;\n  }\n\n  // 检查射线是否与障碍物相交\n  raycast(raycaster) {\n    if (this.group && this.group.children[0]) {\n      const intersects = raycaster.intersectObject(this.group.children[0]);\n      return intersects.length > 0 ? intersects[0] : null;\n    }\n    return null;\n  }\n\n  // 获取障碍物的角点（用于路径规划）\n  getCornerPoints() {\n    const box = this.getBoundingBox();\n    const corners = [new THREE.Vector3(box.min.x, box.min.y, box.min.z), new THREE.Vector3(box.max.x, box.min.y, box.min.z), new THREE.Vector3(box.max.x, box.min.y, box.max.z), new THREE.Vector3(box.min.x, box.min.y, box.max.z), new THREE.Vector3(box.min.x, box.max.y, box.min.z), new THREE.Vector3(box.max.x, box.max.y, box.min.z), new THREE.Vector3(box.max.x, box.max.y, box.max.z), new THREE.Vector3(box.min.x, box.max.y, box.max.z)];\n    return corners;\n  }\n\n  // 获取障碍物周围的导航点（用于路径规划）\n  getNavigationPoints(margin = 0.5) {\n    const box = this.getBoundingBox();\n    const expandedBox = box.clone();\n    expandedBox.expandByScalar(margin);\n\n    // 在障碍物周围创建导航点\n    const navPoints = [new THREE.Vector3(expandedBox.min.x, 0, expandedBox.min.z), new THREE.Vector3(expandedBox.max.x, 0, expandedBox.min.z), new THREE.Vector3(expandedBox.max.x, 0, expandedBox.max.z), new THREE.Vector3(expandedBox.min.x, 0, expandedBox.max.z)];\n    return navPoints;\n  }\n\n  // 克隆障碍物\n  clone() {\n    return new Obstacle(this.id + '_copy', this.name + '_copy', this.position.clone(), this.width, this.height, this.depth);\n  }\n\n  // 销毁对象\n  dispose() {\n    if (this.group) {\n      this.group.children.forEach(child => {\n        if (child.geometry) child.geometry.dispose();\n        if (child.material) child.material.dispose();\n      });\n    }\n  }\n}", "map": {"version": 3, "names": ["THREE", "Obstacle", "constructor", "id", "name", "position", "Vector3", "width", "height", "depth", "type", "clone", "isSelected", "color", "<PERSON><PERSON><PERSON>", "geometry", "BoxGeometry", "material", "MeshLambertMaterial", "transparent", "opacity", "meshObject", "<PERSON><PERSON>", "y", "<PERSON><PERSON><PERSON><PERSON>", "receiveShadow", "wireframeGeometry", "EdgesGeometry", "wireframeMaterial", "LineBasicMaterial", "wireframe", "LineSegments", "visible", "group", "Group", "add", "copy", "userData", "draggable", "objectType", "objectId", "mesh", "updateGeometry", "<PERSON><PERSON><PERSON>", "children", "oldWireframe", "remove", "dispose", "new<PERSON><PERSON>", "newWireframe", "setSelected", "selected", "emissive", "setHex", "updateProperty", "property", "value", "Math", "max", "updatePosition", "newPosition", "getBoundingBox", "box", "Box3", "setFromObject", "containsPoint", "point", "intersectsBox", "otherBox", "intersectsSphere", "center", "radius", "sphere", "Sphere", "getClosestSurfacePoint", "closestPoint", "clamp", "min", "raycast", "raycaster", "intersects", "intersectObject", "length", "getCornerPoints", "corners", "x", "z", "getNavigationPoints", "margin", "expandedBox", "expandByScalar", "navPoints", "for<PERSON>ach", "child"], "sources": ["D:/code/map/my-map/src/classes/Obstacle.js"], "sourcesContent": ["import * as THREE from 'three'\n\nexport class Obstacle {\n  constructor(id, name, position = new THREE.Vector3(0, 0, 0), width = 1, height = 1, depth = 1) {\n    this.id = id\n    this.name = name\n    this.type = 'obstacle'\n    this.position = position.clone()\n    this.width = width\n    this.height = height\n    this.depth = depth\n    this.isSelected = false\n    this.color = 0x8b4513 // 棕色\n    \n    this.createMesh()\n  }\n  \n  createMesh() {\n    // 创建长方体几何体\n    const geometry = new THREE.BoxGeometry(this.width, this.height, this.depth)\n\n    // 创建材质\n    const material = new THREE.MeshLambertMaterial({\n      color: this.color,\n      transparent: true,\n      opacity: 0.8\n    })\n\n    // 创建网格\n    const meshObject = new THREE.Mesh(geometry, material)\n    meshObject.position.y = this.height / 2 // 让底部贴地\n\n    // 启用阴影\n    meshObject.castShadow = true\n    meshObject.receiveShadow = true\n\n    // 创建边框线条（选中时显示）\n    const wireframeGeometry = new THREE.EdgesGeometry(geometry)\n    const wireframeMaterial = new THREE.LineBasicMaterial({\n      color: 0x000000,\n      transparent: true,\n      opacity: 0.3\n    })\n\n    this.wireframe = new THREE.LineSegments(wireframeGeometry, wireframeMaterial)\n    this.wireframe.position.y = this.height / 2\n    this.wireframe.visible = false\n\n    // 创建一个组来包含网格和线框\n    this.group = new THREE.Group()\n    this.group.add(meshObject)\n    this.group.add(this.wireframe)\n\n    // 设置组的位置为对象位置\n    this.group.position.copy(this.position)\n\n    // 设置用户数据在组上\n    this.group.userData = {\n      draggable: true,\n      objectType: 'obstacle',\n      objectId: this.id\n    }\n\n    // 主要引用指向组\n    this.mesh = this.group\n  }\n  \n\n  \n  updateGeometry() {\n    // 移除旧的几何体\n    const oldMesh = this.group.children[0]\n    const oldWireframe = this.group.children[1]\n\n    this.group.remove(oldMesh)\n    this.group.remove(oldWireframe)\n\n    oldMesh.geometry.dispose()\n    oldMesh.material.dispose()\n    oldWireframe.geometry.dispose()\n    oldWireframe.material.dispose()\n\n    // 创建新的几何体\n    const geometry = new THREE.BoxGeometry(this.width, this.height, this.depth)\n    const material = new THREE.MeshLambertMaterial({\n      color: this.color,\n      transparent: true,\n      opacity: 0.8\n    })\n\n    const newMesh = new THREE.Mesh(geometry, material)\n    newMesh.position.y = this.height / 2 // 只设置Y偏移，让底部贴地\n    newMesh.castShadow = true\n    newMesh.receiveShadow = true\n\n    // 创建新的线框\n    const wireframeGeometry = new THREE.EdgesGeometry(geometry)\n    const wireframeMaterial = new THREE.LineBasicMaterial({\n      color: 0x000000,\n      transparent: true,\n      opacity: 0.3\n    })\n\n    const newWireframe = new THREE.LineSegments(wireframeGeometry, wireframeMaterial)\n    newWireframe.position.y = this.height / 2\n    newWireframe.visible = this.isSelected\n\n    this.group.add(newMesh)\n    this.group.add(newWireframe)\n\n    this.wireframe = newWireframe\n  }\n  \n  setSelected(selected) {\n    this.isSelected = selected\n    \n    if (this.wireframe) {\n      this.wireframe.visible = selected\n    }\n    \n    // 高亮效果\n    const meshObject = this.group.children[0]\n    if (meshObject && meshObject.material) {\n      if (selected) {\n        meshObject.material.emissive.setHex(0x444444)\n        meshObject.material.opacity = 1.0\n      } else {\n        meshObject.material.emissive.setHex(0x000000)\n        meshObject.material.opacity = 0.8\n      }\n    }\n  }\n  \n  updateProperty(property, value) {\n    switch (property) {\n      case 'name':\n        this.name = value\n        break\n      case 'width':\n        this.width = Math.max(0.1, value)\n        this.updateGeometry()\n        break\n      case 'height':\n        this.height = Math.max(0.1, value)\n        this.updateGeometry()\n        break\n      case 'depth':\n        this.depth = Math.max(0.1, value)\n        this.updateGeometry()\n        break\n      case 'color': {\n        this.color = value\n        const meshObject = this.group.children[0]\n        if (meshObject && meshObject.material) {\n          meshObject.material.color.setHex(value)\n        }\n        break\n      }\n    }\n  }\n  \n  // 更新位置\n  updatePosition(newPosition) {\n    this.position.copy(newPosition)\n    \n    if (this.group) {\n      this.group.position.copy(newPosition)\n      \n      // 更新内部网格的Y位置以保持底部贴地\n      const meshObject = this.group.children[0]\n      const wireframe = this.group.children[1]\n      \n      if (meshObject) {\n        meshObject.position.y = this.height / 2\n      }\n      if (wireframe) {\n        wireframe.position.y = this.height / 2\n      }\n    }\n  }\n  \n  // 获取边界框（用于碰撞检测）\n  getBoundingBox() {\n    const box = new THREE.Box3()\n    if (this.mesh) {\n      box.setFromObject(this.mesh)\n    }\n    return box\n  }\n  \n  // 检查点是否在障碍物内部\n  containsPoint(point) {\n    const box = this.getBoundingBox()\n    return box.containsPoint(point)\n  }\n  \n  // 检查与另一个边界框是否相交\n  intersectsBox(otherBox) {\n    const box = this.getBoundingBox()\n    return box.intersectsBox(otherBox)\n  }\n  \n  // 检查与球体是否相交（用于人物碰撞检测）\n  intersectsSphere(center, radius) {\n    const box = this.getBoundingBox()\n    const sphere = new THREE.Sphere(center, radius)\n    return box.intersectsSphere(sphere)\n  }\n  \n  // 获取最近的表面点（用于路径规划）\n  getClosestSurfacePoint(point) {\n    const box = this.getBoundingBox()\n    const closestPoint = new THREE.Vector3()\n    \n    // 将点限制在边界框内\n    closestPoint.copy(point)\n    closestPoint.clamp(box.min, box.max)\n    \n    return closestPoint\n  }\n  \n  // 检查射线是否与障碍物相交\n  raycast(raycaster) {\n    if (this.group && this.group.children[0]) {\n      const intersects = raycaster.intersectObject(this.group.children[0])\n      return intersects.length > 0 ? intersects[0] : null\n    }\n    return null\n  }\n  \n  // 获取障碍物的角点（用于路径规划）\n  getCornerPoints() {\n    const box = this.getBoundingBox()\n    const corners = [\n      new THREE.Vector3(box.min.x, box.min.y, box.min.z),\n      new THREE.Vector3(box.max.x, box.min.y, box.min.z),\n      new THREE.Vector3(box.max.x, box.min.y, box.max.z),\n      new THREE.Vector3(box.min.x, box.min.y, box.max.z),\n      new THREE.Vector3(box.min.x, box.max.y, box.min.z),\n      new THREE.Vector3(box.max.x, box.max.y, box.min.z),\n      new THREE.Vector3(box.max.x, box.max.y, box.max.z),\n      new THREE.Vector3(box.min.x, box.max.y, box.max.z)\n    ]\n    return corners\n  }\n  \n  // 获取障碍物周围的导航点（用于路径规划）\n  getNavigationPoints(margin = 0.5) {\n    const box = this.getBoundingBox()\n    const expandedBox = box.clone()\n    expandedBox.expandByScalar(margin)\n    \n    // 在障碍物周围创建导航点\n    const navPoints = [\n      new THREE.Vector3(expandedBox.min.x, 0, expandedBox.min.z),\n      new THREE.Vector3(expandedBox.max.x, 0, expandedBox.min.z),\n      new THREE.Vector3(expandedBox.max.x, 0, expandedBox.max.z),\n      new THREE.Vector3(expandedBox.min.x, 0, expandedBox.max.z)\n    ]\n    \n    return navPoints\n  }\n  \n  // 克隆障碍物\n  clone() {\n    return new Obstacle(\n      this.id + '_copy',\n      this.name + '_copy',\n      this.position.clone(),\n      this.width,\n      this.height,\n      this.depth\n    )\n  }\n  \n  // 销毁对象\n  dispose() {\n    if (this.group) {\n      this.group.children.forEach(child => {\n        if (child.geometry) child.geometry.dispose()\n        if (child.material) child.material.dispose()\n      })\n    }\n  }\n}\n"], "mappings": ";;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAO,MAAMC,QAAQ,CAAC;EACpBC,WAAWA,CAACC,EAAE,EAAEC,IAAI,EAAEC,QAAQ,GAAG,IAAIL,KAAK,CAACM,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAEC,MAAM,GAAG,CAAC,EAAEC,KAAK,GAAG,CAAC,EAAE;IAC7F,IAAI,CAACN,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACM,IAAI,GAAG,UAAU;IACtB,IAAI,CAACL,QAAQ,GAAGA,QAAQ,CAACM,KAAK,CAAC,CAAC;IAChC,IAAI,CAACJ,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACC,MAAM,GAAGA,MAAM;IACpB,IAAI,CAACC,KAAK,GAAGA,KAAK;IAClB,IAAI,CAACG,UAAU,GAAG,KAAK;IACvB,IAAI,CAACC,KAAK,GAAG,QAAQ,EAAC;;IAEtB,IAAI,CAACC,UAAU,CAAC,CAAC;EACnB;EAEAA,UAAUA,CAAA,EAAG;IACX;IACA,MAAMC,QAAQ,GAAG,IAAIf,KAAK,CAACgB,WAAW,CAAC,IAAI,CAACT,KAAK,EAAE,IAAI,CAACC,MAAM,EAAE,IAAI,CAACC,KAAK,CAAC;;IAE3E;IACA,MAAMQ,QAAQ,GAAG,IAAIjB,KAAK,CAACkB,mBAAmB,CAAC;MAC7CL,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBM,WAAW,EAAE,IAAI;MACjBC,OAAO,EAAE;IACX,CAAC,CAAC;;IAEF;IACA,MAAMC,UAAU,GAAG,IAAIrB,KAAK,CAACsB,IAAI,CAACP,QAAQ,EAAEE,QAAQ,CAAC;IACrDI,UAAU,CAAChB,QAAQ,CAACkB,CAAC,GAAG,IAAI,CAACf,MAAM,GAAG,CAAC,EAAC;;IAExC;IACAa,UAAU,CAACG,UAAU,GAAG,IAAI;IAC5BH,UAAU,CAACI,aAAa,GAAG,IAAI;;IAE/B;IACA,MAAMC,iBAAiB,GAAG,IAAI1B,KAAK,CAAC2B,aAAa,CAACZ,QAAQ,CAAC;IAC3D,MAAMa,iBAAiB,GAAG,IAAI5B,KAAK,CAAC6B,iBAAiB,CAAC;MACpDhB,KAAK,EAAE,QAAQ;MACfM,WAAW,EAAE,IAAI;MACjBC,OAAO,EAAE;IACX,CAAC,CAAC;IAEF,IAAI,CAACU,SAAS,GAAG,IAAI9B,KAAK,CAAC+B,YAAY,CAACL,iBAAiB,EAAEE,iBAAiB,CAAC;IAC7E,IAAI,CAACE,SAAS,CAACzB,QAAQ,CAACkB,CAAC,GAAG,IAAI,CAACf,MAAM,GAAG,CAAC;IAC3C,IAAI,CAACsB,SAAS,CAACE,OAAO,GAAG,KAAK;;IAE9B;IACA,IAAI,CAACC,KAAK,GAAG,IAAIjC,KAAK,CAACkC,KAAK,CAAC,CAAC;IAC9B,IAAI,CAACD,KAAK,CAACE,GAAG,CAACd,UAAU,CAAC;IAC1B,IAAI,CAACY,KAAK,CAACE,GAAG,CAAC,IAAI,CAACL,SAAS,CAAC;;IAE9B;IACA,IAAI,CAACG,KAAK,CAAC5B,QAAQ,CAAC+B,IAAI,CAAC,IAAI,CAAC/B,QAAQ,CAAC;;IAEvC;IACA,IAAI,CAAC4B,KAAK,CAACI,QAAQ,GAAG;MACpBC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,UAAU;MACtBC,QAAQ,EAAE,IAAI,CAACrC;IACjB,CAAC;;IAED;IACA,IAAI,CAACsC,IAAI,GAAG,IAAI,CAACR,KAAK;EACxB;EAIAS,cAAcA,CAAA,EAAG;IACf;IACA,MAAMC,OAAO,GAAG,IAAI,CAACV,KAAK,CAACW,QAAQ,CAAC,CAAC,CAAC;IACtC,MAAMC,YAAY,GAAG,IAAI,CAACZ,KAAK,CAACW,QAAQ,CAAC,CAAC,CAAC;IAE3C,IAAI,CAACX,KAAK,CAACa,MAAM,CAACH,OAAO,CAAC;IAC1B,IAAI,CAACV,KAAK,CAACa,MAAM,CAACD,YAAY,CAAC;IAE/BF,OAAO,CAAC5B,QAAQ,CAACgC,OAAO,CAAC,CAAC;IAC1BJ,OAAO,CAAC1B,QAAQ,CAAC8B,OAAO,CAAC,CAAC;IAC1BF,YAAY,CAAC9B,QAAQ,CAACgC,OAAO,CAAC,CAAC;IAC/BF,YAAY,CAAC5B,QAAQ,CAAC8B,OAAO,CAAC,CAAC;;IAE/B;IACA,MAAMhC,QAAQ,GAAG,IAAIf,KAAK,CAACgB,WAAW,CAAC,IAAI,CAACT,KAAK,EAAE,IAAI,CAACC,MAAM,EAAE,IAAI,CAACC,KAAK,CAAC;IAC3E,MAAMQ,QAAQ,GAAG,IAAIjB,KAAK,CAACkB,mBAAmB,CAAC;MAC7CL,KAAK,EAAE,IAAI,CAACA,KAAK;MACjBM,WAAW,EAAE,IAAI;MACjBC,OAAO,EAAE;IACX,CAAC,CAAC;IAEF,MAAM4B,OAAO,GAAG,IAAIhD,KAAK,CAACsB,IAAI,CAACP,QAAQ,EAAEE,QAAQ,CAAC;IAClD+B,OAAO,CAAC3C,QAAQ,CAACkB,CAAC,GAAG,IAAI,CAACf,MAAM,GAAG,CAAC,EAAC;IACrCwC,OAAO,CAACxB,UAAU,GAAG,IAAI;IACzBwB,OAAO,CAACvB,aAAa,GAAG,IAAI;;IAE5B;IACA,MAAMC,iBAAiB,GAAG,IAAI1B,KAAK,CAAC2B,aAAa,CAACZ,QAAQ,CAAC;IAC3D,MAAMa,iBAAiB,GAAG,IAAI5B,KAAK,CAAC6B,iBAAiB,CAAC;MACpDhB,KAAK,EAAE,QAAQ;MACfM,WAAW,EAAE,IAAI;MACjBC,OAAO,EAAE;IACX,CAAC,CAAC;IAEF,MAAM6B,YAAY,GAAG,IAAIjD,KAAK,CAAC+B,YAAY,CAACL,iBAAiB,EAAEE,iBAAiB,CAAC;IACjFqB,YAAY,CAAC5C,QAAQ,CAACkB,CAAC,GAAG,IAAI,CAACf,MAAM,GAAG,CAAC;IACzCyC,YAAY,CAACjB,OAAO,GAAG,IAAI,CAACpB,UAAU;IAEtC,IAAI,CAACqB,KAAK,CAACE,GAAG,CAACa,OAAO,CAAC;IACvB,IAAI,CAACf,KAAK,CAACE,GAAG,CAACc,YAAY,CAAC;IAE5B,IAAI,CAACnB,SAAS,GAAGmB,YAAY;EAC/B;EAEAC,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAI,CAACvC,UAAU,GAAGuC,QAAQ;IAE1B,IAAI,IAAI,CAACrB,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAACE,OAAO,GAAGmB,QAAQ;IACnC;;IAEA;IACA,MAAM9B,UAAU,GAAG,IAAI,CAACY,KAAK,CAACW,QAAQ,CAAC,CAAC,CAAC;IACzC,IAAIvB,UAAU,IAAIA,UAAU,CAACJ,QAAQ,EAAE;MACrC,IAAIkC,QAAQ,EAAE;QACZ9B,UAAU,CAACJ,QAAQ,CAACmC,QAAQ,CAACC,MAAM,CAAC,QAAQ,CAAC;QAC7ChC,UAAU,CAACJ,QAAQ,CAACG,OAAO,GAAG,GAAG;MACnC,CAAC,MAAM;QACLC,UAAU,CAACJ,QAAQ,CAACmC,QAAQ,CAACC,MAAM,CAAC,QAAQ,CAAC;QAC7ChC,UAAU,CAACJ,QAAQ,CAACG,OAAO,GAAG,GAAG;MACnC;IACF;EACF;EAEAkC,cAAcA,CAACC,QAAQ,EAAEC,KAAK,EAAE;IAC9B,QAAQD,QAAQ;MACd,KAAK,MAAM;QACT,IAAI,CAACnD,IAAI,GAAGoD,KAAK;QACjB;MACF,KAAK,OAAO;QACV,IAAI,CAACjD,KAAK,GAAGkD,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEF,KAAK,CAAC;QACjC,IAAI,CAACd,cAAc,CAAC,CAAC;QACrB;MACF,KAAK,QAAQ;QACX,IAAI,CAAClC,MAAM,GAAGiD,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEF,KAAK,CAAC;QAClC,IAAI,CAACd,cAAc,CAAC,CAAC;QACrB;MACF,KAAK,OAAO;QACV,IAAI,CAACjC,KAAK,GAAGgD,IAAI,CAACC,GAAG,CAAC,GAAG,EAAEF,KAAK,CAAC;QACjC,IAAI,CAACd,cAAc,CAAC,CAAC;QACrB;MACF,KAAK,OAAO;QAAE;UACZ,IAAI,CAAC7B,KAAK,GAAG2C,KAAK;UAClB,MAAMnC,UAAU,GAAG,IAAI,CAACY,KAAK,CAACW,QAAQ,CAAC,CAAC,CAAC;UACzC,IAAIvB,UAAU,IAAIA,UAAU,CAACJ,QAAQ,EAAE;YACrCI,UAAU,CAACJ,QAAQ,CAACJ,KAAK,CAACwC,MAAM,CAACG,KAAK,CAAC;UACzC;UACA;QACF;IACF;EACF;;EAEA;EACAG,cAAcA,CAACC,WAAW,EAAE;IAC1B,IAAI,CAACvD,QAAQ,CAAC+B,IAAI,CAACwB,WAAW,CAAC;IAE/B,IAAI,IAAI,CAAC3B,KAAK,EAAE;MACd,IAAI,CAACA,KAAK,CAAC5B,QAAQ,CAAC+B,IAAI,CAACwB,WAAW,CAAC;;MAErC;MACA,MAAMvC,UAAU,GAAG,IAAI,CAACY,KAAK,CAACW,QAAQ,CAAC,CAAC,CAAC;MACzC,MAAMd,SAAS,GAAG,IAAI,CAACG,KAAK,CAACW,QAAQ,CAAC,CAAC,CAAC;MAExC,IAAIvB,UAAU,EAAE;QACdA,UAAU,CAAChB,QAAQ,CAACkB,CAAC,GAAG,IAAI,CAACf,MAAM,GAAG,CAAC;MACzC;MACA,IAAIsB,SAAS,EAAE;QACbA,SAAS,CAACzB,QAAQ,CAACkB,CAAC,GAAG,IAAI,CAACf,MAAM,GAAG,CAAC;MACxC;IACF;EACF;;EAEA;EACAqD,cAAcA,CAAA,EAAG;IACf,MAAMC,GAAG,GAAG,IAAI9D,KAAK,CAAC+D,IAAI,CAAC,CAAC;IAC5B,IAAI,IAAI,CAACtB,IAAI,EAAE;MACbqB,GAAG,CAACE,aAAa,CAAC,IAAI,CAACvB,IAAI,CAAC;IAC9B;IACA,OAAOqB,GAAG;EACZ;;EAEA;EACAG,aAAaA,CAACC,KAAK,EAAE;IACnB,MAAMJ,GAAG,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;IACjC,OAAOC,GAAG,CAACG,aAAa,CAACC,KAAK,CAAC;EACjC;;EAEA;EACAC,aAAaA,CAACC,QAAQ,EAAE;IACtB,MAAMN,GAAG,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;IACjC,OAAOC,GAAG,CAACK,aAAa,CAACC,QAAQ,CAAC;EACpC;;EAEA;EACAC,gBAAgBA,CAACC,MAAM,EAAEC,MAAM,EAAE;IAC/B,MAAMT,GAAG,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;IACjC,MAAMW,MAAM,GAAG,IAAIxE,KAAK,CAACyE,MAAM,CAACH,MAAM,EAAEC,MAAM,CAAC;IAC/C,OAAOT,GAAG,CAACO,gBAAgB,CAACG,MAAM,CAAC;EACrC;;EAEA;EACAE,sBAAsBA,CAACR,KAAK,EAAE;IAC5B,MAAMJ,GAAG,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;IACjC,MAAMc,YAAY,GAAG,IAAI3E,KAAK,CAACM,OAAO,CAAC,CAAC;;IAExC;IACAqE,YAAY,CAACvC,IAAI,CAAC8B,KAAK,CAAC;IACxBS,YAAY,CAACC,KAAK,CAACd,GAAG,CAACe,GAAG,EAAEf,GAAG,CAACJ,GAAG,CAAC;IAEpC,OAAOiB,YAAY;EACrB;;EAEA;EACAG,OAAOA,CAACC,SAAS,EAAE;IACjB,IAAI,IAAI,CAAC9C,KAAK,IAAI,IAAI,CAACA,KAAK,CAACW,QAAQ,CAAC,CAAC,CAAC,EAAE;MACxC,MAAMoC,UAAU,GAAGD,SAAS,CAACE,eAAe,CAAC,IAAI,CAAChD,KAAK,CAACW,QAAQ,CAAC,CAAC,CAAC,CAAC;MACpE,OAAOoC,UAAU,CAACE,MAAM,GAAG,CAAC,GAAGF,UAAU,CAAC,CAAC,CAAC,GAAG,IAAI;IACrD;IACA,OAAO,IAAI;EACb;;EAEA;EACAG,eAAeA,CAAA,EAAG;IAChB,MAAMrB,GAAG,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;IACjC,MAAMuB,OAAO,GAAG,CACd,IAAIpF,KAAK,CAACM,OAAO,CAACwD,GAAG,CAACe,GAAG,CAACQ,CAAC,EAAEvB,GAAG,CAACe,GAAG,CAACtD,CAAC,EAAEuC,GAAG,CAACe,GAAG,CAACS,CAAC,CAAC,EAClD,IAAItF,KAAK,CAACM,OAAO,CAACwD,GAAG,CAACJ,GAAG,CAAC2B,CAAC,EAAEvB,GAAG,CAACe,GAAG,CAACtD,CAAC,EAAEuC,GAAG,CAACe,GAAG,CAACS,CAAC,CAAC,EAClD,IAAItF,KAAK,CAACM,OAAO,CAACwD,GAAG,CAACJ,GAAG,CAAC2B,CAAC,EAAEvB,GAAG,CAACe,GAAG,CAACtD,CAAC,EAAEuC,GAAG,CAACJ,GAAG,CAAC4B,CAAC,CAAC,EAClD,IAAItF,KAAK,CAACM,OAAO,CAACwD,GAAG,CAACe,GAAG,CAACQ,CAAC,EAAEvB,GAAG,CAACe,GAAG,CAACtD,CAAC,EAAEuC,GAAG,CAACJ,GAAG,CAAC4B,CAAC,CAAC,EAClD,IAAItF,KAAK,CAACM,OAAO,CAACwD,GAAG,CAACe,GAAG,CAACQ,CAAC,EAAEvB,GAAG,CAACJ,GAAG,CAACnC,CAAC,EAAEuC,GAAG,CAACe,GAAG,CAACS,CAAC,CAAC,EAClD,IAAItF,KAAK,CAACM,OAAO,CAACwD,GAAG,CAACJ,GAAG,CAAC2B,CAAC,EAAEvB,GAAG,CAACJ,GAAG,CAACnC,CAAC,EAAEuC,GAAG,CAACe,GAAG,CAACS,CAAC,CAAC,EAClD,IAAItF,KAAK,CAACM,OAAO,CAACwD,GAAG,CAACJ,GAAG,CAAC2B,CAAC,EAAEvB,GAAG,CAACJ,GAAG,CAACnC,CAAC,EAAEuC,GAAG,CAACJ,GAAG,CAAC4B,CAAC,CAAC,EAClD,IAAItF,KAAK,CAACM,OAAO,CAACwD,GAAG,CAACe,GAAG,CAACQ,CAAC,EAAEvB,GAAG,CAACJ,GAAG,CAACnC,CAAC,EAAEuC,GAAG,CAACJ,GAAG,CAAC4B,CAAC,CAAC,CACnD;IACD,OAAOF,OAAO;EAChB;;EAEA;EACAG,mBAAmBA,CAACC,MAAM,GAAG,GAAG,EAAE;IAChC,MAAM1B,GAAG,GAAG,IAAI,CAACD,cAAc,CAAC,CAAC;IACjC,MAAM4B,WAAW,GAAG3B,GAAG,CAACnD,KAAK,CAAC,CAAC;IAC/B8E,WAAW,CAACC,cAAc,CAACF,MAAM,CAAC;;IAElC;IACA,MAAMG,SAAS,GAAG,CAChB,IAAI3F,KAAK,CAACM,OAAO,CAACmF,WAAW,CAACZ,GAAG,CAACQ,CAAC,EAAE,CAAC,EAAEI,WAAW,CAACZ,GAAG,CAACS,CAAC,CAAC,EAC1D,IAAItF,KAAK,CAACM,OAAO,CAACmF,WAAW,CAAC/B,GAAG,CAAC2B,CAAC,EAAE,CAAC,EAAEI,WAAW,CAACZ,GAAG,CAACS,CAAC,CAAC,EAC1D,IAAItF,KAAK,CAACM,OAAO,CAACmF,WAAW,CAAC/B,GAAG,CAAC2B,CAAC,EAAE,CAAC,EAAEI,WAAW,CAAC/B,GAAG,CAAC4B,CAAC,CAAC,EAC1D,IAAItF,KAAK,CAACM,OAAO,CAACmF,WAAW,CAACZ,GAAG,CAACQ,CAAC,EAAE,CAAC,EAAEI,WAAW,CAAC/B,GAAG,CAAC4B,CAAC,CAAC,CAC3D;IAED,OAAOK,SAAS;EAClB;;EAEA;EACAhF,KAAKA,CAAA,EAAG;IACN,OAAO,IAAIV,QAAQ,CACjB,IAAI,CAACE,EAAE,GAAG,OAAO,EACjB,IAAI,CAACC,IAAI,GAAG,OAAO,EACnB,IAAI,CAACC,QAAQ,CAACM,KAAK,CAAC,CAAC,EACrB,IAAI,CAACJ,KAAK,EACV,IAAI,CAACC,MAAM,EACX,IAAI,CAACC,KACP,CAAC;EACH;;EAEA;EACAsC,OAAOA,CAAA,EAAG;IACR,IAAI,IAAI,CAACd,KAAK,EAAE;MACd,IAAI,CAACA,KAAK,CAACW,QAAQ,CAACgD,OAAO,CAACC,KAAK,IAAI;QACnC,IAAIA,KAAK,CAAC9E,QAAQ,EAAE8E,KAAK,CAAC9E,QAAQ,CAACgC,OAAO,CAAC,CAAC;QAC5C,IAAI8C,KAAK,CAAC5E,QAAQ,EAAE4E,KAAK,CAAC5E,QAAQ,CAAC8B,OAAO,CAAC,CAAC;MAC9C,CAAC,CAAC;IACJ;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}