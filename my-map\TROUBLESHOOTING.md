# 故障排除指南

## 已修复的问题

### 2. 路径拖拽错误 - "Cannot read properties of undefined (reading 'x')"

**错误信息:**
```
Cannot read properties of undefined (reading 'x')
```

**原因:**
1. 路径创建时只传递了一个点，但Path类需要至少两个点才能创建曲线
2. 点对象可能为null或undefined
3. 点对象可能不是有效的THREE.Vector3对象

**解决方案:**
1. **在Map3D组件中修复路径创建:**
```javascript
case 'path': {
  // 为路径创建两个点，第二个点稍微偏移
  const startPoint = position.clone()
  const endPoint = position.clone().add(new THREE.Vector3(2, 0, 0))
  object = makeThreeObjectNonReactive(new Path(id, name, [startPoint, endPoint]))
  break
}
```

2. **在Path类构造函数中添加验证:**
```javascript
// 确保points是有效的THREE.Vector3数组
this.points = []
if (Array.isArray(points)) {
  points.forEach(point => {
    if (point && typeof point.clone === 'function') {
      this.points.push(point.clone())
    } else if (point && typeof point.x === 'number' && typeof point.y === 'number' && typeof point.z === 'number') {
      this.points.push(new THREE.Vector3(point.x, point.y, point.z))
    }
  })
}

// 如果没有足够的点，创建默认的两个点
if (this.points.length < 2) {
  this.points = [
    new THREE.Vector3(0, 0, 0),
    new THREE.Vector3(2, 0, 0)
  ]
}
```

3. **在createSmoothCurve方法中添加安全检查:**
```javascript
// 验证点的有效性
const validPoints = this.points.filter(point =>
  point &&
  typeof point.x === 'number' &&
  typeof point.y === 'number' &&
  typeof point.z === 'number' &&
  !isNaN(point.x) && !isNaN(point.y) && !isNaN(point.z)
)

if (validPoints.length < 2) {
  return new THREE.LineCurve3(
    new THREE.Vector3(0, 0, 0),
    new THREE.Vector3(1, 0, 0)
  )
}
```

**修复位置:**
- `src/components/Map3D.vue` - createObjectAtPosition、createObject方法
- `src/classes/Path.js` - 构造函数、createSmoothCurve、updatePathLine、createControlPoints、createControlPoint、addDirectionArrows方法

**最终修复 (第4次):**
发现问题的根本原因是Vue模板中访问 `selectedObject.position.x` 时，Path对象没有position属性。

**关键修复:**
1. **Vue模板安全访问**: 使用 `selectedObject.position?.x || 0` 防止undefined访问
2. **Path类添加position属性**: 为兼容性添加position属性
3. **updateObjectPosition方法**: 根据对象类型进行不同处理
4. **控制点重新创建**: 修复createControlPoints方法以支持重新创建

**测试验证:**
- `debug-path.html` - 基础测试
- `test-complete.html` - 完整功能测试

---

### 1. Three.js modelViewMatrix 错误

**错误信息:**
```
Uncaught TypeError: 'get' on proxy: property 'modelViewMatrix' is a read-only and non-configurable data property on the proxy target but the proxy did not return its actual value
```

**原因:**
Vue 3的响应式系统会自动将对象包装成Proxy，但Three.js对象包含许多只读属性和复杂的内部结构，与Vue的响应式系统冲突。

**解决方案:**
使用Vue 3的`markRaw()`函数将Three.js对象标记为非响应式：

```javascript
import { markRaw } from 'vue'

// 创建非响应式的Three.js对象
this.scene = markRaw(new THREE.Scene())
this.camera = markRaw(new THREE.PerspectiveCamera(...))
this.renderer = markRaw(new THREE.WebGLRenderer(...))

// 或使用工具函数
import { createNonReactiveThreeObject, makeThreeObjectNonReactive } from '../utils/threeUtils.js'

this.scene = createNonReactiveThreeObject(THREE.Scene)
this.camera = createNonReactiveThreeObject(THREE.PerspectiveCamera, 75, width/height, 0.1, 1000)
```

**修复位置:**
- `src/components/Map3D.vue` - 所有Three.js对象创建
- `src/utils/threeUtils.js` - 工具函数
- `src/classes/Person.js`, `src/classes/Path.js`, `src/classes/Obstacle.js` - 对象类

### 2. ESLint 错误修复

**错误类型:**
- `no-case-declarations` - case块中的词法声明
- `no-unused-vars` - 未使用的变量

**解决方案:**
- 在case块中使用大括号包装变量声明
- 移除未使用的参数

## 性能优化建议

### 1. 对象池
对于频繁创建/销毁的对象，考虑使用对象池：

```javascript
class ObjectPool {
  constructor(createFn, resetFn) {
    this.createFn = createFn
    this.resetFn = resetFn
    this.pool = []
  }
  
  get() {
    return this.pool.pop() || this.createFn()
  }
  
  release(obj) {
    this.resetFn(obj)
    this.pool.push(obj)
  }
}
```

### 2. LOD (Level of Detail)
根据距离调整对象细节：

```javascript
updateLOD(camera) {
  const distance = camera.position.distanceTo(this.position)
  if (distance > 50) {
    this.mesh.visible = false
  } else if (distance > 20) {
    // 使用低细节模型
  } else {
    // 使用高细节模型
  }
}
```

### 3. 批量更新
避免在每帧中进行昂贵的操作：

```javascript
// 不好的做法
animate() {
  this.objects.forEach(obj => obj.expensiveUpdate())
}

// 好的做法
animate() {
  this.frameCount++
  if (this.frameCount % 10 === 0) {
    this.objects.forEach(obj => obj.expensiveUpdate())
  }
}
```

## 常见问题

### Q: 拖拽不工作
A: 检查对象的`userData.draggable`属性是否设置为true

### Q: 对象不显示
A: 检查对象是否添加到场景中，材质是否正确设置

### Q: 性能问题
A: 检查对象数量，考虑使用LOD或对象池

### Q: 路径编辑不工作
A: 确保路径对象的`isEditing`属性正确设置

## 调试技巧

### 1. Three.js Inspector
在浏览器控制台中访问Three.js对象：
```javascript
// 在Map3D组件中添加
window.debugScene = this.scene
window.debugCamera = this.camera
window.debugRenderer = this.renderer
```

### 2. 性能监控
```javascript
// 添加性能监控
const stats = new Stats()
document.body.appendChild(stats.dom)

// 在animate循环中
stats.begin()
// 渲染代码
stats.end()
```

### 3. 对象检查
```javascript
// 检查对象状态
console.log('Scene children:', this.scene.children.length)
console.log('Objects:', this.sceneObjects.map(obj => ({
  id: obj.id,
  type: obj.type,
  position: obj.position
})))
```

## 🎉 最新修复状态 - 完整拖拽功能实现

### ✅ 已完全实现的功能
- **所有对象拖拽**: 人物、路线、障碍物都可以拖拽移动
- **坐标实时更新**: 拖拽后属性面板立即显示新坐标
- **智能对象检测**: DragManager支持Group对象的拖拽检测
- **类型特化处理**: 不同对象类型有专门的拖拽逻辑
- **界面同步**: Vue界面与3D场景完全同步

### 🔧 关键技术实现
1. **DragManager.findDraggableParent()**: 支持嵌套对象拖拽检测
2. **位置更新回调机制**: 拖拽时实时同步对象状态
3. **强制界面更新**: 确保属性面板显示最新坐标
4. **轨道控制器管理**: 拖拽时正确禁用/启用相机控制

### 📋 测试验证
- `test-drag-final.html`: 完整的拖拽功能测试页面
- 所有对象类型的拖拽和坐标更新都已验证通过

### 🎮 使用方法
1. 创建任意对象（人物、路线、障碍物）
2. 点击并拖拽对象到新位置
3. 选择对象查看属性面板中的坐标更新
4. 所有功能都应该正常工作

**现在所有拖拽功能都已完美实现！** 🚀

---

## 🗑️ 路线功能移除 (最新更新)

### 移除原因
用户不再需要路线功能，要求完全移除所有路线相关的代码。

### 已移除的内容

#### 1. **文件删除**
- `src/classes/Path.js` - 路线类文件
- 所有测试文件 (test-*.html)

#### 2. **Map3D.vue 组件清理**
- 移除路线拖拽元素
- 移除 Path 类导入
- 移除 createObject 中的 path case
- 移除 duplicateSelectedObject 中的 path case
- 移除 onDraggedObjectPositionUpdate 中的 path case
- 移除路线特有属性编辑界面
- 移除路径编辑功能 (togglePathEditing, addPathPoint)
- 移除人物沿路径移动功能 (movePersonAlongPath, stopPersonMovement)
- 移除 canMovePersonAlongPath computed 属性
- 移除 isPathEditMode data 属性
- 更新 getObjectIcon 和 getObjectTypeName 方法

#### 3. **Person.js 类简化**
- 移除路径移动相关属性 (isMoving, currentPath, pathProgress, moveSpeed)
- 移除路径移动相关方法 (updateWalkAnimation, updatePathMovement, calculatePathDistance, startMoving, stopMoving)
- 简化 update 方法
- 移除 updateProperty 中的 moveSpeed 处理

### 🎯 当前功能状态

#### ✅ 保留的功能
- **👤 人物对象**: 创建、拖拽、属性编辑
- **🧱 障碍物对象**: 创建、拖拽、属性编辑 (尺寸、位置)
- **3D场景控制**: 旋转、缩放、平移
- **对象管理**: 选择、删除、复制
- **拖拽功能**: 所有对象都可以拖拽移动
- **坐标更新**: 拖拽后实时更新坐标信息

#### ❌ 已移除的功能
- 路线创建和编辑
- 路径控制点编辑
- 人物沿路径移动
- 路径相关的所有属性和方法

### 📊 项目状态
- **编译状态**: ✅ 成功编译
- **运行状态**: ✅ 正常运行在 http://localhost:8080/
- **功能完整性**: ✅ 人物和障碍物功能完全正常
- **代码清洁度**: ✅ 所有路线相关代码已清理

**项目现在只包含人物和障碍物功能，运行完全正常！** 🎉
