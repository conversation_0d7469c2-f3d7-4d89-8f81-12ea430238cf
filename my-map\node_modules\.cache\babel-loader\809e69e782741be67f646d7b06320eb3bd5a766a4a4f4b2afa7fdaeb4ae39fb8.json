{"ast": null, "code": "import \"core-js/modules/es.array.push.js\";\nimport \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.filter.js\";\nimport \"core-js/modules/es.iterator.find.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport * as THREE from 'three';\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js';\nimport { Person } from '../classes/Person.js';\nimport { DragManager } from '../managers/DragManager.js';\nimport { markRaw } from 'vue';\nimport { createNonReactiveThreeObject, makeThreeObjectNonReactive } from '../utils/threeUtils.js';\nexport default {\n  name: 'Map3D',\n  data() {\n    return {\n      scene: null,\n      camera: null,\n      renderer: null,\n      controls: null,\n      dragManager: null,\n      sceneObjects: [],\n      selectedObject: null,\n      nextId: 1\n    };\n  },\n  mounted() {\n    this.initThreeJS();\n    this.setupDragAndDrop();\n    this.animate();\n  },\n  beforeUnmount() {\n    if (this.renderer) {\n      this.renderer.dispose();\n    }\n  },\n  methods: {\n    initThreeJS() {\n      // 创建场景\n      this.scene = createNonReactiveThreeObject(THREE.Scene);\n      this.scene.background = new THREE.Color(0xf0f0f0);\n\n      // 创建相机\n      const container = this.$refs.threeContainer;\n      this.camera = createNonReactiveThreeObject(THREE.PerspectiveCamera, 75, container.clientWidth / container.clientHeight, 0.1, 1000);\n      this.camera.position.set(10, 10, 10);\n\n      // 创建渲染器\n      this.renderer = createNonReactiveThreeObject(THREE.WebGLRenderer, {\n        antialias: true\n      });\n      this.renderer.setSize(container.clientWidth, container.clientHeight);\n      this.renderer.shadowMap.enabled = true;\n      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap;\n      container.appendChild(this.renderer.domElement);\n\n      // 添加控制器\n      this.controls = makeThreeObjectNonReactive(new OrbitControls(this.camera, this.renderer.domElement));\n      this.controls.enableDamping = true;\n      this.controls.dampingFactor = 0.05;\n\n      // 添加光源\n      this.setupLighting();\n\n      // 添加地面\n      this.createFloor();\n\n      // 初始化拖拽管理器\n      this.dragManager = markRaw(new DragManager(this.scene, this.camera, this.renderer.domElement));\n      this.dragManager.setControls(this.controls);\n      this.dragManager.setObjectPositionUpdateCallback(this.onDraggedObjectPositionUpdate);\n\n      // 监听窗口大小变化\n      window.addEventListener('resize', this.onWindowResize);\n\n      // 监听点击事件\n      this.renderer.domElement.addEventListener('click', this.onCanvasClick);\n    },\n    setupLighting() {\n      // 环境光\n      const ambientLight = new THREE.AmbientLight(0x404040, 0.6);\n      this.scene.add(ambientLight);\n\n      // 方向光\n      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8);\n      directionalLight.position.set(10, 10, 5);\n      directionalLight.castShadow = true;\n      directionalLight.shadow.mapSize.width = 2048;\n      directionalLight.shadow.mapSize.height = 2048;\n      this.scene.add(directionalLight);\n    },\n    // 创建地板\n    createFloor() {\n      const geometry = new THREE.PlaneGeometry(30, 30);\n      const material = new THREE.MeshLambertMaterial({\n        color: 0xcccccc,\n        transparent: true,\n        opacity: 0.8\n      });\n      const floor = new THREE.Mesh(geometry, material);\n      floor.rotation.x = -Math.PI / 2;\n      floor.receiveShadow = true;\n      this.scene.add(floor);\n\n      // 添加网格\n      const gridHelper = new THREE.GridHelper(30, 30, 0x888888, 0xaaaaaa);\n      this.scene.add(gridHelper);\n\n      // 添加坐标轴辅助器（可选，用于调试）\n      const axesHelper = new THREE.AxesHelper(5);\n      this.scene.add(axesHelper);\n      // 红色=X轴(左右), 绿色=Y轴(上下), 蓝色=Z轴(前后)\n    },\n    setupDragAndDrop() {\n      const container = this.$refs.threeContainer;\n      container.addEventListener('dragover', e => {\n        e.preventDefault();\n      });\n      container.addEventListener('drop', e => {\n        e.preventDefault();\n        const objectType = e.dataTransfer.getData('text/plain');\n        this.createObjectAtPosition(objectType, e);\n      });\n    },\n    startDrag(type, event) {\n      event.dataTransfer.setData('text/plain', type);\n    },\n    createObjectAtPosition(type, event) {\n      const rect = this.$refs.threeContainer.getBoundingClientRect();\n      const mouse = new THREE.Vector2();\n      mouse.x = (event.clientX - rect.left) / rect.width * 2 - 1;\n      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;\n      const raycaster = new THREE.Raycaster();\n      raycaster.setFromCamera(mouse, this.camera);\n\n      // 与地面相交\n      const plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0);\n      const intersectPoint = new THREE.Vector3();\n\n      // 检查射线与平面的交点\n      const intersection = raycaster.ray.intersectPlane(plane, intersectPoint);\n\n      // 如果没有交点，使用默认位置\n      if (!intersection) {\n        console.warn('No intersection with ground plane, using default position');\n        intersectPoint.set(0, 0, 0);\n      }\n\n      // 验证intersectPoint是有效的\n      if (!intersectPoint || typeof intersectPoint.x !== 'number' || isNaN(intersectPoint.x)) {\n        console.warn('Invalid intersect point, using default position');\n        intersectPoint.set(0, 0, 0);\n      }\n      this.createObject(type, intersectPoint);\n    },\n    createObject(type, position) {\n      // 验证position参数\n      if (!position || typeof position.x !== 'number' || typeof position.y !== 'number' || typeof position.z !== 'number') {\n        console.error('createObject: Invalid position parameter:', position);\n        position = new THREE.Vector3(0, 0, 0);\n      }\n      let object;\n      const id = this.nextId++;\n      const name = `${this.getObjectTypeName(type)}${id}`;\n      try {\n        switch (type) {\n          case 'person':\n            object = makeThreeObjectNonReactive(new Person(id, name, position));\n            break;\n          default:\n            console.warn('createObject: Unknown object type:', type);\n            return;\n        }\n        if (!object || !object.mesh) {\n          console.error('createObject: Failed to create object or mesh');\n          return;\n        }\n        this.scene.add(object.mesh);\n        this.sceneObjects.push(object);\n        this.selectedObject = object;\n\n        // Object created successfully\n      } catch (error) {\n        console.error(`Error creating ${type} object:`, error);\n      }\n    },\n    selectObject(object) {\n      // 取消之前选中对象的高亮\n      if (this.selectedObject && this.selectedObject.setSelected) {\n        this.selectedObject.setSelected(false);\n      }\n      this.selectedObject = object;\n\n      // 高亮当前选中对象\n      if (object && object.setSelected) {\n        object.setSelected(true);\n      }\n\n      // 强制更新Vue界面\n      this.$forceUpdate();\n    },\n    deleteObject(id) {\n      const index = this.sceneObjects.findIndex(obj => obj.id === id);\n      if (index !== -1) {\n        const object = this.sceneObjects[index];\n        this.scene.remove(object.mesh);\n        this.sceneObjects.splice(index, 1);\n        if (this.selectedObject && this.selectedObject.id === id) {\n          this.selectedObject = null;\n        }\n      }\n    },\n    updateObjectProperty(property, value) {\n      if (this.selectedObject && this.selectedObject.updateProperty) {\n        this.selectedObject.updateProperty(property, value);\n      }\n    },\n    updateObjectPosition(axis, value) {\n      if (this.selectedObject && this.selectedObject.position) {\n        this.selectedObject.position[axis] = value;\n\n        // 根据对象类型进行不同的处理\n        if (this.selectedObject.type === 'path') {\n          // 对于路径，移动第一个点\n          if (this.selectedObject.points && this.selectedObject.points.length > 0) {\n            this.selectedObject.points[0][axis] = value;\n            // 重新创建路径\n            this.selectedObject.updatePathLine();\n            this.selectedObject.createControlPoints();\n          }\n        } else {\n          // 对于人物和其他对象\n          if (this.selectedObject.mesh) {\n            this.selectedObject.mesh.position[axis] = value;\n          }\n        }\n      }\n    },\n    getObjectIcon(type) {\n      const icons = {\n        person: '👤'\n      };\n      return icons[type] || '❓';\n    },\n    getObjectTypeName(type) {\n      const names = {\n        person: '人物'\n      };\n      return names[type] || '对象';\n    },\n    onCanvasClick(event) {\n      const rect = this.$refs.threeContainer.getBoundingClientRect();\n      const mouse = new THREE.Vector2();\n      mouse.x = (event.clientX - rect.left) / rect.width * 2 - 1;\n      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1;\n      const raycaster = new THREE.Raycaster();\n      raycaster.setFromCamera(mouse, this.camera);\n\n      // 递归检测所有子对象，包括Group内部的mesh\n      const intersects = raycaster.intersectObjects(this.sceneObjects.map(obj => obj.mesh).filter(mesh => mesh), true // 递归检测子对象\n      );\n      if (intersects.length > 0) {\n        const clickedMesh = intersects[0].object;\n\n        // 查找对应的场景对象，需要考虑Group结构\n        let clickedObject = null;\n\n        // 首先尝试直接匹配\n        clickedObject = this.sceneObjects.find(obj => obj.mesh === clickedMesh);\n\n        // 如果没找到，可能是Group内部的子对象，向上查找\n        if (!clickedObject) {\n          let parent = clickedMesh.parent;\n          while (parent && !clickedObject) {\n            clickedObject = this.sceneObjects.find(obj => obj.mesh === parent);\n            parent = parent.parent;\n          }\n        }\n\n        // 还可以通过userData查找\n        if (!clickedObject) {\n          // 检查点击的mesh的userData\n          if (clickedMesh.userData && clickedMesh.userData.objectType && clickedMesh.userData.objectId) {\n            const {\n              objectType,\n              objectId\n            } = clickedMesh.userData;\n            clickedObject = this.sceneObjects.find(obj => obj.type === objectType && obj.id === objectId);\n          }\n\n          // 检查父级的userData\n          if (!clickedObject && clickedMesh.parent && clickedMesh.parent.userData) {\n            const {\n              objectType,\n              objectId\n            } = clickedMesh.parent.userData;\n            if (objectType && objectId) {\n              clickedObject = this.sceneObjects.find(obj => obj.type === objectType && obj.id === objectId);\n            }\n          }\n        }\n        if (clickedObject) {\n          this.selectObject(clickedObject);\n        } else {\n          this.selectObject(null);\n        }\n      } else {\n        this.selectObject(null);\n      }\n    },\n    onWindowResize() {\n      const container = this.$refs.threeContainer;\n      this.camera.aspect = container.clientWidth / container.clientHeight;\n      this.camera.updateProjectionMatrix();\n      this.renderer.setSize(container.clientWidth, container.clientHeight);\n    },\n    // 持续更新渲染\n    animate() {\n      requestAnimationFrame(this.animate);\n\n      // 更新相机控制器\n      this.controls.update();\n\n      // 更新所有场景对象\n      this.sceneObjects.forEach(obj => {\n        if (obj.update) {\n          obj.update();\n        }\n      });\n\n      // 渲染场景\n      this.renderer.render(this.scene, this.camera);\n    },\n    // 复制对象\n    duplicateSelectedObject() {\n      if (!this.selectedObject) return;\n      const offset = new THREE.Vector3(1, 0, 1);\n      const newPosition = this.selectedObject.position.clone().add(offset);\n      switch (this.selectedObject.type) {\n        case 'person':\n          {\n            this.createObject('person', newPosition);\n            break;\n          }\n      }\n    },\n    // 清空场景\n    clearScene() {\n      if (confirm('确定要清空整个场景吗？此操作不可撤销。')) {\n        // 移除所有对象\n        this.sceneObjects.forEach(obj => {\n          this.scene.remove(obj.mesh);\n          if (obj.dispose) {\n            obj.dispose();\n          }\n        });\n        this.sceneObjects = [];\n        this.selectedObject = null;\n        this.nextId = 1;\n      }\n    },\n    // 拖拽对象位置更新回调\n    onDraggedObjectPositionUpdate(objectType, objectId, newPosition) {\n      // 找到对应的对象\n      const object = this.sceneObjects.find(obj => obj.id === objectId && obj.type === objectType);\n      if (!object) {\n        return;\n      }\n\n      // 更新对象的内部位置\n      object.position.copy(newPosition);\n\n      // 根据对象类型进行特殊处理\n      switch (objectType) {\n        case 'person':\n          // 人物对象：确保mesh位置同步\n          if (object.mesh) {\n            object.mesh.position.copy(newPosition);\n          }\n          break;\n      }\n\n      // 如果当前选中的是这个对象，触发界面更新\n      if (this.selectedObject && this.selectedObject.id === objectId) {\n        // 强制Vue更新界面\n        this.$forceUpdate();\n      }\n    }\n  }\n};", "map": {"version": 3, "names": ["THREE", "OrbitControls", "Person", "Drag<PERSON>anager", "mark<PERSON>aw", "createNonReactiveThreeObject", "makeThreeObjectNonReactive", "name", "data", "scene", "camera", "renderer", "controls", "dragManager", "sceneObjects", "selectedObject", "nextId", "mounted", "initThreeJS", "setupDragAndDrop", "animate", "beforeUnmount", "dispose", "methods", "Scene", "background", "Color", "container", "$refs", "threeContainer", "PerspectiveCamera", "clientWidth", "clientHeight", "position", "set", "WebGLRenderer", "antialias", "setSize", "shadowMap", "enabled", "type", "PCFSoftShadowMap", "append<PERSON><PERSON><PERSON>", "dom<PERSON>lement", "enableDamping", "dampingFactor", "setupLighting", "createFloor", "setControls", "setObjectPositionUpdateCallback", "onDraggedObjectPositionUpdate", "window", "addEventListener", "onWindowResize", "onCanvasClick", "ambientLight", "AmbientLight", "add", "directionalLight", "DirectionalLight", "<PERSON><PERSON><PERSON><PERSON>", "shadow", "mapSize", "width", "height", "geometry", "PlaneGeometry", "material", "MeshLambertMaterial", "color", "transparent", "opacity", "floor", "<PERSON><PERSON>", "rotation", "x", "Math", "PI", "receiveShadow", "gridHelper", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "axes<PERSON>elper", "AxesHelper", "e", "preventDefault", "objectType", "dataTransfer", "getData", "createObjectAtPosition", "startDrag", "event", "setData", "rect", "getBoundingClientRect", "mouse", "Vector2", "clientX", "left", "y", "clientY", "top", "raycaster", "Raycaster", "setFromCamera", "plane", "Plane", "Vector3", "intersectPoint", "intersection", "ray", "intersectPlane", "console", "warn", "isNaN", "createObject", "z", "error", "object", "id", "getObjectTypeName", "mesh", "push", "selectObject", "setSelected", "$forceUpdate", "deleteObject", "index", "findIndex", "obj", "remove", "splice", "updateObjectProperty", "property", "value", "updateProperty", "updateObjectPosition", "axis", "points", "length", "updatePathLine", "createControlPoints", "getObjectIcon", "icons", "person", "names", "intersects", "intersectObjects", "map", "filter", "<PERSON><PERSON><PERSON>", "clickedObject", "find", "parent", "userData", "objectId", "aspect", "updateProjectionMatrix", "requestAnimationFrame", "update", "for<PERSON>ach", "render", "duplicateSelectedObject", "offset", "newPosition", "clone", "clearScene", "confirm", "copy"], "sources": ["D:\\code\\map\\my-map\\src\\components\\Map3D.vue"], "sourcesContent": ["<template>\n  <div class=\"map3d-container\">\n    <div ref=\"threeContainer\" class=\"three-container\"></div>\n    <div class=\"control-panel\">\n      <div class=\"drag-items\">\n        <h3>拖拽元素</h3>\n        <div class=\"drag-item\" draggable=\"true\" @dragstart=\"startDrag('person', $event)\">\n          <span>👤 人物</span>\n        </div>\n      </div>\n      \n      <div  v-if=\"sceneObjects && sceneObjects.length\" class=\"object-list\">\n        <h3>场景对象</h3>\n        <div v-for=\"obj in sceneObjects\" :key=\"obj.id\" class=\"object-item\" \n             :class=\"{ selected: selectedObject?.id === obj.id }\"\n             @click=\"selectObject(obj)\">\n          <span>{{ getObjectIcon(obj.type) }} {{ obj.name }}</span>\n          <button @click.stop=\"deleteObject(obj.id)\" class=\"delete-btn\">×</button>\n        </div>\n      </div>\n      \n      <div v-if=\"selectedObject\" class=\"object-properties\">\n        <h3>属性编辑</h3>\n        <div class=\"property-group\">\n          <label>名称:</label>\n          <input v-model=\"selectedObject.name\" @input=\"updateObjectProperty('name', $event.target.value)\">\n        </div>\n        <div class=\"property-group\">\n          <label>X坐标:</label>\n          <input type=\"number\" :value=\"selectedObject.position?.x || 0\"\n                 @input=\"updateObjectPosition('x', parseFloat($event.target.value))\">\n        </div>\n        <div class=\"property-group\">\n          <label>Y坐标:</label>\n          <input type=\"number\" :value=\"selectedObject.position?.y || 0\"\n                 @input=\"updateObjectPosition('y', parseFloat($event.target.value))\">\n        </div>\n        <div class=\"property-group\">\n          <label>Z坐标:</label>\n          <input type=\"number\" :value=\"selectedObject.position?.z || 0\"\n                 @input=\"updateObjectPosition('z', parseFloat($event.target.value))\">\n        </div>\n\n        <!-- 人物特有属性 -->\n        <div v-if=\"selectedObject.type === 'person'\" class=\"property-group\">\n          <label>朝向角度:</label>\n          <input type=\"number\" :value=\"Math.round(selectedObject.getRotationDegrees ? selectedObject.getRotationDegrees() : 0)\"\n                 @input=\"updateObjectProperty('rotation', parseFloat($event.target.value) * Math.PI / 180)\"\n                 min=\"0\" max=\"360\" step=\"15\">\n          <small style=\"display: block; color: #666; margin-top: 5px;\">\n            0°=正面朝前, 90°=朝右, 180°=朝后, 270°=朝左\n          </small>\n        </div>\n      </div>\n\n      <!-- 操作按钮 -->\n      <div v-if=\"sceneObjects && sceneObjects.length\" class=\"action-buttons\">\n        <button @click=\"duplicateSelectedObject\" class=\"action-btn\" v-if=\"selectedObject\">\n          复制对象\n        </button>\n        <button @click=\"clearScene\" class=\"action-btn danger\">\n          清空场景\n        </button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as THREE from 'three'\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'\nimport { Person } from '../classes/Person.js'\nimport { DragManager } from '../managers/DragManager.js'\nimport { markRaw } from 'vue'\nimport { createNonReactiveThreeObject, makeThreeObjectNonReactive } from '../utils/threeUtils.js'\n\nexport default {\n  name: 'Map3D',\n  data() {\n    return {\n      scene: null,\n      camera: null,\n      renderer: null,\n      controls: null,\n      dragManager: null,\n      sceneObjects: [],\n      selectedObject: null,\n      nextId: 1\n    }\n  },\n\n  mounted() {\n    this.initThreeJS()\n    this.setupDragAndDrop()\n    this.animate()\n  },\n  beforeUnmount() {\n    if (this.renderer) {\n      this.renderer.dispose()\n    }\n  },\n  methods: {\n    initThreeJS() {\n      // 创建场景\n      this.scene = createNonReactiveThreeObject(THREE.Scene)\n      this.scene.background = new THREE.Color(0xf0f0f0)\n\n      // 创建相机\n      const container = this.$refs.threeContainer\n      this.camera = createNonReactiveThreeObject(THREE.PerspectiveCamera,\n        75,\n        container.clientWidth / container.clientHeight,\n        0.1,\n        1000\n      )\n      this.camera.position.set(10, 10, 10)\n\n      // 创建渲染器\n      this.renderer = createNonReactiveThreeObject(THREE.WebGLRenderer, { antialias: true })\n      this.renderer.setSize(container.clientWidth, container.clientHeight)\n\n      this.renderer.shadowMap.enabled = true\n      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap\n      container.appendChild(this.renderer.domElement)\n\n      // 添加控制器\n      this.controls = makeThreeObjectNonReactive(new OrbitControls(this.camera, this.renderer.domElement))\n      this.controls.enableDamping = true\n      this.controls.dampingFactor = 0.05\n      \n      // 添加光源\n      this.setupLighting()\n      \n      // 添加地面\n      this.createFloor()\n      \n      // 初始化拖拽管理器\n      this.dragManager = markRaw(new DragManager(this.scene, this.camera, this.renderer.domElement))\n      this.dragManager.setControls(this.controls)\n      this.dragManager.setObjectPositionUpdateCallback(this.onDraggedObjectPositionUpdate)\n\n      // 监听窗口大小变化\n      window.addEventListener('resize', this.onWindowResize)\n\n      // 监听点击事件\n      this.renderer.domElement.addEventListener('click', this.onCanvasClick)\n    },\n    \n    setupLighting() {\n      // 环境光\n      const ambientLight = new THREE.AmbientLight(0x404040, 0.6)\n      this.scene.add(ambientLight)\n      \n      // 方向光\n      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)\n      directionalLight.position.set(10, 10, 5)\n      directionalLight.castShadow = true\n      directionalLight.shadow.mapSize.width = 2048\n      directionalLight.shadow.mapSize.height = 2048\n      this.scene.add(directionalLight)\n    },\n    \n    // 创建地板\n    createFloor() {\n      const geometry = new THREE.PlaneGeometry(30, 30)\n      const material = new THREE.MeshLambertMaterial({\n        color: 0xcccccc,\n        transparent: true,\n        opacity: 0.8\n      })\n      const floor = new THREE.Mesh(geometry, material)\n      floor.rotation.x = -Math.PI / 2\n      floor.receiveShadow = true\n      this.scene.add(floor)\n\n      // 添加网格\n      const gridHelper = new THREE.GridHelper(30, 30, 0x888888, 0xaaaaaa)\n      this.scene.add(gridHelper)\n\n      // 添加坐标轴辅助器（可选，用于调试）\n      const axesHelper = new THREE.AxesHelper(5)\n      this.scene.add(axesHelper)\n      // 红色=X轴(左右), 绿色=Y轴(上下), 蓝色=Z轴(前后)\n    },\n\n    setupDragAndDrop() {\n      const container = this.$refs.threeContainer\n\n      container.addEventListener('dragover', (e) => {\n        e.preventDefault()\n      })\n\n      container.addEventListener('drop', (e) => {\n        e.preventDefault()\n        const objectType = e.dataTransfer.getData('text/plain')\n        this.createObjectAtPosition(objectType, e)\n      })\n    },\n\n    startDrag(type, event) {\n      event.dataTransfer.setData('text/plain', type)\n    },\n\n    createObjectAtPosition(type, event) {\n      const rect = this.$refs.threeContainer.getBoundingClientRect()\n      const mouse = new THREE.Vector2()\n      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1\n      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1\n\n      const raycaster = new THREE.Raycaster()\n      raycaster.setFromCamera(mouse, this.camera)\n\n      // 与地面相交\n      const plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0)\n      const intersectPoint = new THREE.Vector3()\n\n      // 检查射线与平面的交点\n      const intersection = raycaster.ray.intersectPlane(plane, intersectPoint)\n\n      // 如果没有交点，使用默认位置\n      if (!intersection) {\n        console.warn('No intersection with ground plane, using default position')\n        intersectPoint.set(0, 0, 0)\n      }\n\n      // 验证intersectPoint是有效的\n      if (!intersectPoint || typeof intersectPoint.x !== 'number' || isNaN(intersectPoint.x)) {\n        console.warn('Invalid intersect point, using default position')\n        intersectPoint.set(0, 0, 0)\n      }\n\n      this.createObject(type, intersectPoint)\n    },\n\n    createObject(type, position) {\n      // 验证position参数\n      if (!position || typeof position.x !== 'number' || typeof position.y !== 'number' || typeof position.z !== 'number') {\n        console.error('createObject: Invalid position parameter:', position)\n        position = new THREE.Vector3(0, 0, 0)\n      }\n\n      let object\n      const id = this.nextId++\n      const name = `${this.getObjectTypeName(type)}${id}`\n\n      try {\n        switch (type) {\n          case 'person':\n            object = makeThreeObjectNonReactive(new Person(id, name, position))\n            break\n          default:\n            console.warn('createObject: Unknown object type:', type)\n            return\n        }\n\n        if (!object || !object.mesh) {\n          console.error('createObject: Failed to create object or mesh')\n          return\n        }\n\n        this.scene.add(object.mesh)\n        this.sceneObjects.push(object)\n        this.selectedObject = object\n\n        // Object created successfully\n      } catch (error) {\n        console.error(`Error creating ${type} object:`, error)\n      }\n    },\n\n    selectObject(object) {\n      // 取消之前选中对象的高亮\n      if (this.selectedObject && this.selectedObject.setSelected) {\n        this.selectedObject.setSelected(false)\n      }\n\n      this.selectedObject = object\n\n      // 高亮当前选中对象\n      if (object && object.setSelected) {\n        object.setSelected(true)\n      }\n\n      // 强制更新Vue界面\n      this.$forceUpdate()\n    },\n\n    deleteObject(id) {\n      const index = this.sceneObjects.findIndex(obj => obj.id === id)\n      if (index !== -1) {\n        const object = this.sceneObjects[index]\n        this.scene.remove(object.mesh)\n        this.sceneObjects.splice(index, 1)\n\n        if (this.selectedObject && this.selectedObject.id === id) {\n          this.selectedObject = null\n        }\n      }\n    },\n\n    updateObjectProperty(property, value) {\n      if (this.selectedObject && this.selectedObject.updateProperty) {\n        this.selectedObject.updateProperty(property, value)\n      }\n    },\n\n    updateObjectPosition(axis, value) {\n      if (this.selectedObject && this.selectedObject.position) {\n        this.selectedObject.position[axis] = value\n\n        // 根据对象类型进行不同的处理\n        if (this.selectedObject.type === 'path') {\n          // 对于路径，移动第一个点\n          if (this.selectedObject.points && this.selectedObject.points.length > 0) {\n            this.selectedObject.points[0][axis] = value\n            // 重新创建路径\n            this.selectedObject.updatePathLine()\n            this.selectedObject.createControlPoints()\n          }\n        } else {\n          // 对于人物和其他对象\n          if (this.selectedObject.mesh) {\n            this.selectedObject.mesh.position[axis] = value\n          }\n        }\n      }\n    },\n\n    getObjectIcon(type) {\n      const icons = {\n        person: '👤',\n      }\n      return icons[type] || '❓'\n    },\n\n    getObjectTypeName(type) {\n      const names = {\n        person: '人物',\n      }\n      return names[type] || '对象'\n    },\n\n    onCanvasClick(event) {\n      const rect = this.$refs.threeContainer.getBoundingClientRect()\n      const mouse = new THREE.Vector2()\n      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1\n      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1\n\n      const raycaster = new THREE.Raycaster()\n      raycaster.setFromCamera(mouse, this.camera)\n\n      // 递归检测所有子对象，包括Group内部的mesh\n      const intersects = raycaster.intersectObjects(\n        this.sceneObjects.map(obj => obj.mesh).filter(mesh => mesh),\n        true // 递归检测子对象\n      )\n\n      if (intersects.length > 0) {\n        const clickedMesh = intersects[0].object\n\n        // 查找对应的场景对象，需要考虑Group结构\n        let clickedObject = null\n\n        // 首先尝试直接匹配\n        clickedObject = this.sceneObjects.find(obj => obj.mesh === clickedMesh)\n\n        // 如果没找到，可能是Group内部的子对象，向上查找\n        if (!clickedObject) {\n          let parent = clickedMesh.parent\n          while (parent && !clickedObject) {\n            clickedObject = this.sceneObjects.find(obj => obj.mesh === parent)\n            parent = parent.parent\n          }\n        }\n\n        // 还可以通过userData查找\n        if (!clickedObject) {\n          // 检查点击的mesh的userData\n          if (clickedMesh.userData && clickedMesh.userData.objectType && clickedMesh.userData.objectId) {\n            const { objectType, objectId } = clickedMesh.userData\n            clickedObject = this.sceneObjects.find(obj =>\n              obj.type === objectType && obj.id === objectId\n            )\n          }\n\n          // 检查父级的userData\n          if (!clickedObject && clickedMesh.parent && clickedMesh.parent.userData) {\n            const { objectType, objectId } = clickedMesh.parent.userData\n            if (objectType && objectId) {\n              clickedObject = this.sceneObjects.find(obj =>\n                obj.type === objectType && obj.id === objectId\n              )\n            }\n          }\n        }\n\n        if (clickedObject) {\n          this.selectObject(clickedObject)\n        } else {\n          this.selectObject(null)\n        }\n      } else {\n        this.selectObject(null)\n      }\n    },\n\n    onWindowResize() {\n      const container = this.$refs.threeContainer\n      this.camera.aspect = container.clientWidth / container.clientHeight\n      this.camera.updateProjectionMatrix()\n      this.renderer.setSize(container.clientWidth, container.clientHeight)\n    },\n\n    // 持续更新渲染\n    animate() {\n      requestAnimationFrame(this.animate)\n\n      // 更新相机控制器\n      this.controls.update()\n\n      // 更新所有场景对象\n      this.sceneObjects.forEach(obj => {\n        if (obj.update) {\n          obj.update()\n        }\n      })\n\n      // 渲染场景\n      this.renderer.render(this.scene, this.camera)\n    },\n\n\n\n    // 复制对象\n    duplicateSelectedObject() {\n      if (!this.selectedObject) return\n\n      const offset = new THREE.Vector3(1, 0, 1)\n      const newPosition = this.selectedObject.position.clone().add(offset)\n\n      switch (this.selectedObject.type) {\n        case 'person': {\n          this.createObject('person', newPosition)\n          break\n        }\n      }\n    },\n\n    // 清空场景\n    clearScene() {\n      if (confirm('确定要清空整个场景吗？此操作不可撤销。')) {\n        // 移除所有对象\n        this.sceneObjects.forEach(obj => {\n          this.scene.remove(obj.mesh)\n          if (obj.dispose) {\n            obj.dispose()\n          }\n        })\n\n        this.sceneObjects = []\n        this.selectedObject = null\n        this.nextId = 1\n      }\n    },\n\n    // 拖拽对象位置更新回调\n    onDraggedObjectPositionUpdate(objectType, objectId, newPosition) {\n      // 找到对应的对象\n      const object = this.sceneObjects.find(obj => obj.id === objectId && obj.type === objectType)\n      if (!object) {\n        return\n      }\n\n      // 更新对象的内部位置\n      object.position.copy(newPosition)\n\n      // 根据对象类型进行特殊处理\n      switch (objectType) {\n        case 'person':\n          // 人物对象：确保mesh位置同步\n          if (object.mesh) {\n            object.mesh.position.copy(newPosition)\n          }\n          break\n      }\n\n      // 如果当前选中的是这个对象，触发界面更新\n      if (this.selectedObject && this.selectedObject.id === objectId) {\n        // 强制Vue更新界面\n        this.$forceUpdate()\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.map3d-container {\n  display: flex;\n  height: 100vh;\n  width: 100%;\n}\n\n.three-container {\n  flex: 1;\n  position: relative;\n  overflow: hidden;\n}\n\n.control-panel {\n  width: 300px;\n  background: #f5f5f5;\n  border-left: 1px solid #ddd;\n  padding: 20px;\n  overflow-y: auto;\n}\n\n.control-panel h3 {\n  margin: 0 0 15px 0;\n  color: #333;\n  font-size: 16px;\n  border-bottom: 2px solid #007bff;\n  padding-bottom: 5px;\n}\n\n.drag-items {\n  margin-bottom: 30px;\n}\n\n.drag-item {\n  background: #fff;\n  border: 2px dashed #007bff;\n  border-radius: 8px;\n  padding: 15px;\n  margin-bottom: 10px;\n  cursor: grab;\n  text-align: center;\n  transition: all 0.3s ease;\n}\n\n.drag-item:hover {\n  background: #e3f2fd;\n  border-color: #0056b3;\n  transform: translateY(-2px);\n}\n\n.drag-item:active {\n  cursor: grabbing;\n}\n\n.drag-item span {\n  font-size: 14px;\n  font-weight: 500;\n  color: #007bff;\n}\n\n.object-list {\n  margin-bottom: 30px;\n}\n\n.object-item {\n  background: #fff;\n  border: 1px solid #ddd;\n  border-radius: 6px;\n  padding: 10px;\n  margin-bottom: 8px;\n  cursor: pointer;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  transition: all 0.2s ease;\n}\n\n.object-item:hover {\n  background: #f8f9fa;\n  border-color: #007bff;\n}\n\n.object-item.selected {\n  background: #e3f2fd;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.delete-btn {\n  background: #dc3545;\n  color: white;\n  border: none;\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  cursor: pointer;\n  font-size: 16px;\n  line-height: 1;\n  transition: background-color 0.2s ease;\n}\n\n.delete-btn:hover {\n  background: #c82333;\n}\n\n.object-properties {\n  background: #fff;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  padding: 15px;\n}\n\n.property-group {\n  margin-bottom: 15px;\n}\n\n.property-group label {\n  display: block;\n  margin-bottom: 5px;\n  font-weight: 500;\n  color: #555;\n  font-size: 14px;\n}\n\n.property-group input {\n  width: 100%;\n  padding: 8px 12px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n  transition: border-color 0.2s ease;\n}\n\n.property-group input:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.property-group input[type=\"color\"] {\n  height: 40px;\n  padding: 2px;\n}\n\n.edit-btn, .add-btn, .move-btn, .stop-btn, .action-btn {\n  padding: 8px 16px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  margin: 5px 5px 5px 0;\n  transition: background-color 0.2s ease;\n}\n\n.edit-btn {\n  background: #17a2b8;\n  color: white;\n}\n\n.edit-btn:hover {\n  background: #138496;\n}\n\n.add-btn {\n  background: #28a745;\n  color: white;\n}\n\n.add-btn:hover {\n  background: #218838;\n}\n\n.move-btn {\n  background: #007bff;\n  color: white;\n}\n\n.move-btn:hover {\n  background: #0056b3;\n}\n\n.move-btn:disabled {\n  background: #6c757d;\n  cursor: not-allowed;\n}\n\n.stop-btn {\n  background: #dc3545;\n  color: white;\n}\n\n.stop-btn:hover {\n  background: #c82333;\n}\n\n.action-buttons {\n  margin-top: 20px;\n  padding-top: 20px;\n  border-top: 1px solid #ddd;\n}\n\n.action-btn {\n  background: #6c757d;\n  color: white;\n  width: 100%;\n  margin-bottom: 10px;\n}\n\n.action-btn:hover {\n  background: #545b62;\n}\n\n.action-btn.danger {\n  background: #dc3545;\n}\n\n.action-btn.danger:hover {\n  background: #c82333;\n}\n</style>\n"], "mappings": ";;;;;;AAqEA,OAAO,KAAKA,KAAI,MAAO,OAAM;AAC7B,SAASC,aAAY,QAAS,8CAA6C;AAC3E,SAASC,MAAK,QAAS,sBAAqB;AAC5C,SAASC,WAAU,QAAS,4BAA2B;AACvD,SAASC,OAAM,QAAS,KAAI;AAC5B,SAASC,4BAA4B,EAAEC,0BAAyB,QAAS,wBAAuB;AAEhG,eAAe;EACbC,IAAI,EAAE,OAAO;EACbC,IAAIA,CAAA,EAAG;IACL,OAAO;MACLC,KAAK,EAAE,IAAI;MACXC,MAAM,EAAE,IAAI;MACZC,QAAQ,EAAE,IAAI;MACdC,QAAQ,EAAE,IAAI;MACdC,WAAW,EAAE,IAAI;MACjBC,YAAY,EAAE,EAAE;MAChBC,cAAc,EAAE,IAAI;MACpBC,MAAM,EAAE;IACV;EACF,CAAC;EAEDC,OAAOA,CAAA,EAAG;IACR,IAAI,CAACC,WAAW,CAAC;IACjB,IAAI,CAACC,gBAAgB,CAAC;IACtB,IAAI,CAACC,OAAO,CAAC;EACf,CAAC;EACDC,aAAaA,CAAA,EAAG;IACd,IAAI,IAAI,CAACV,QAAQ,EAAE;MACjB,IAAI,CAACA,QAAQ,CAACW,OAAO,CAAC;IACxB;EACF,CAAC;EACDC,OAAO,EAAE;IACPL,WAAWA,CAAA,EAAG;MACZ;MACA,IAAI,CAACT,KAAI,GAAIJ,4BAA4B,CAACL,KAAK,CAACwB,KAAK;MACrD,IAAI,CAACf,KAAK,CAACgB,UAAS,GAAI,IAAIzB,KAAK,CAAC0B,KAAK,CAAC,QAAQ;;MAEhD;MACA,MAAMC,SAAQ,GAAI,IAAI,CAACC,KAAK,CAACC,cAAa;MAC1C,IAAI,CAACnB,MAAK,GAAIL,4BAA4B,CAACL,KAAK,CAAC8B,iBAAiB,EAChE,EAAE,EACFH,SAAS,CAACI,WAAU,GAAIJ,SAAS,CAACK,YAAY,EAC9C,GAAG,EACH,IACF;MACA,IAAI,CAACtB,MAAM,CAACuB,QAAQ,CAACC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,EAAE;;MAEnC;MACA,IAAI,CAACvB,QAAO,GAAIN,4BAA4B,CAACL,KAAK,CAACmC,aAAa,EAAE;QAAEC,SAAS,EAAE;MAAK,CAAC;MACrF,IAAI,CAACzB,QAAQ,CAAC0B,OAAO,CAACV,SAAS,CAACI,WAAW,EAAEJ,SAAS,CAACK,YAAY;MAEnE,IAAI,CAACrB,QAAQ,CAAC2B,SAAS,CAACC,OAAM,GAAI,IAAG;MACrC,IAAI,CAAC5B,QAAQ,CAAC2B,SAAS,CAACE,IAAG,GAAIxC,KAAK,CAACyC,gBAAe;MACpDd,SAAS,CAACe,WAAW,CAAC,IAAI,CAAC/B,QAAQ,CAACgC,UAAU;;MAE9C;MACA,IAAI,CAAC/B,QAAO,GAAIN,0BAA0B,CAAC,IAAIL,aAAa,CAAC,IAAI,CAACS,MAAM,EAAE,IAAI,CAACC,QAAQ,CAACgC,UAAU,CAAC;MACnG,IAAI,CAAC/B,QAAQ,CAACgC,aAAY,GAAI,IAAG;MACjC,IAAI,CAAChC,QAAQ,CAACiC,aAAY,GAAI,IAAG;;MAEjC;MACA,IAAI,CAACC,aAAa,CAAC;;MAEnB;MACA,IAAI,CAACC,WAAW,CAAC;;MAEjB;MACA,IAAI,CAAClC,WAAU,GAAIT,OAAO,CAAC,IAAID,WAAW,CAAC,IAAI,CAACM,KAAK,EAAE,IAAI,CAACC,MAAM,EAAE,IAAI,CAACC,QAAQ,CAACgC,UAAU,CAAC;MAC7F,IAAI,CAAC9B,WAAW,CAACmC,WAAW,CAAC,IAAI,CAACpC,QAAQ;MAC1C,IAAI,CAACC,WAAW,CAACoC,+BAA+B,CAAC,IAAI,CAACC,6BAA6B;;MAEnF;MACAC,MAAM,CAACC,gBAAgB,CAAC,QAAQ,EAAE,IAAI,CAACC,cAAc;;MAErD;MACA,IAAI,CAAC1C,QAAQ,CAACgC,UAAU,CAACS,gBAAgB,CAAC,OAAO,EAAE,IAAI,CAACE,aAAa;IACvE,CAAC;IAEDR,aAAaA,CAAA,EAAG;MACd;MACA,MAAMS,YAAW,GAAI,IAAIvD,KAAK,CAACwD,YAAY,CAAC,QAAQ,EAAE,GAAG;MACzD,IAAI,CAAC/C,KAAK,CAACgD,GAAG,CAACF,YAAY;;MAE3B;MACA,MAAMG,gBAAe,GAAI,IAAI1D,KAAK,CAAC2D,gBAAgB,CAAC,QAAQ,EAAE,GAAG;MACjED,gBAAgB,CAACzB,QAAQ,CAACC,GAAG,CAAC,EAAE,EAAE,EAAE,EAAE,CAAC;MACvCwB,gBAAgB,CAACE,UAAS,GAAI,IAAG;MACjCF,gBAAgB,CAACG,MAAM,CAACC,OAAO,CAACC,KAAI,GAAI,IAAG;MAC3CL,gBAAgB,CAACG,MAAM,CAACC,OAAO,CAACE,MAAK,GAAI,IAAG;MAC5C,IAAI,CAACvD,KAAK,CAACgD,GAAG,CAACC,gBAAgB;IACjC,CAAC;IAED;IACAX,WAAWA,CAAA,EAAG;MACZ,MAAMkB,QAAO,GAAI,IAAIjE,KAAK,CAACkE,aAAa,CAAC,EAAE,EAAE,EAAE;MAC/C,MAAMC,QAAO,GAAI,IAAInE,KAAK,CAACoE,mBAAmB,CAAC;QAC7CC,KAAK,EAAE,QAAQ;QACfC,WAAW,EAAE,IAAI;QACjBC,OAAO,EAAE;MACX,CAAC;MACD,MAAMC,KAAI,GAAI,IAAIxE,KAAK,CAACyE,IAAI,CAACR,QAAQ,EAAEE,QAAQ;MAC/CK,KAAK,CAACE,QAAQ,CAACC,CAAA,GAAI,CAACC,IAAI,CAACC,EAAC,GAAI;MAC9BL,KAAK,CAACM,aAAY,GAAI,IAAG;MACzB,IAAI,CAACrE,KAAK,CAACgD,GAAG,CAACe,KAAK;;MAEpB;MACA,MAAMO,UAAS,GAAI,IAAI/E,KAAK,CAACgF,UAAU,CAAC,EAAE,EAAE,EAAE,EAAE,QAAQ,EAAE,QAAQ;MAClE,IAAI,CAACvE,KAAK,CAACgD,GAAG,CAACsB,UAAU;;MAEzB;MACA,MAAME,UAAS,GAAI,IAAIjF,KAAK,CAACkF,UAAU,CAAC,CAAC;MACzC,IAAI,CAACzE,KAAK,CAACgD,GAAG,CAACwB,UAAU;MACzB;IACF,CAAC;IAED9D,gBAAgBA,CAAA,EAAG;MACjB,MAAMQ,SAAQ,GAAI,IAAI,CAACC,KAAK,CAACC,cAAa;MAE1CF,SAAS,CAACyB,gBAAgB,CAAC,UAAU,EAAG+B,CAAC,IAAK;QAC5CA,CAAC,CAACC,cAAc,CAAC;MACnB,CAAC;MAEDzD,SAAS,CAACyB,gBAAgB,CAAC,MAAM,EAAG+B,CAAC,IAAK;QACxCA,CAAC,CAACC,cAAc,CAAC;QACjB,MAAMC,UAAS,GAAIF,CAAC,CAACG,YAAY,CAACC,OAAO,CAAC,YAAY;QACtD,IAAI,CAACC,sBAAsB,CAACH,UAAU,EAAEF,CAAC;MAC3C,CAAC;IACH,CAAC;IAEDM,SAASA,CAACjD,IAAI,EAAEkD,KAAK,EAAE;MACrBA,KAAK,CAACJ,YAAY,CAACK,OAAO,CAAC,YAAY,EAAEnD,IAAI;IAC/C,CAAC;IAEDgD,sBAAsBA,CAAChD,IAAI,EAAEkD,KAAK,EAAE;MAClC,MAAME,IAAG,GAAI,IAAI,CAAChE,KAAK,CAACC,cAAc,CAACgE,qBAAqB,CAAC;MAC7D,MAAMC,KAAI,GAAI,IAAI9F,KAAK,CAAC+F,OAAO,CAAC;MAChCD,KAAK,CAACnB,CAAA,GAAK,CAACe,KAAK,CAACM,OAAM,GAAIJ,IAAI,CAACK,IAAI,IAAIL,IAAI,CAAC7B,KAAK,GAAI,IAAI;MAC3D+B,KAAK,CAACI,CAAA,GAAI,EAAE,CAACR,KAAK,CAACS,OAAM,GAAIP,IAAI,CAACQ,GAAG,IAAIR,IAAI,CAAC5B,MAAM,IAAI,IAAI;MAE5D,MAAMqC,SAAQ,GAAI,IAAIrG,KAAK,CAACsG,SAAS,CAAC;MACtCD,SAAS,CAACE,aAAa,CAACT,KAAK,EAAE,IAAI,CAACpF,MAAM;;MAE1C;MACA,MAAM8F,KAAI,GAAI,IAAIxG,KAAK,CAACyG,KAAK,CAAC,IAAIzG,KAAK,CAAC0G,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE,CAAC;MAC3D,MAAMC,cAAa,GAAI,IAAI3G,KAAK,CAAC0G,OAAO,CAAC;;MAEzC;MACA,MAAME,YAAW,GAAIP,SAAS,CAACQ,GAAG,CAACC,cAAc,CAACN,KAAK,EAAEG,cAAc;;MAEvE;MACA,IAAI,CAACC,YAAY,EAAE;QACjBG,OAAO,CAACC,IAAI,CAAC,2DAA2D;QACxEL,cAAc,CAACzE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MAC5B;;MAEA;MACA,IAAI,CAACyE,cAAa,IAAK,OAAOA,cAAc,CAAChC,CAAA,KAAM,QAAO,IAAKsC,KAAK,CAACN,cAAc,CAAChC,CAAC,CAAC,EAAE;QACtFoC,OAAO,CAACC,IAAI,CAAC,iDAAiD;QAC9DL,cAAc,CAACzE,GAAG,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MAC5B;MAEA,IAAI,CAACgF,YAAY,CAAC1E,IAAI,EAAEmE,cAAc;IACxC,CAAC;IAEDO,YAAYA,CAAC1E,IAAI,EAAEP,QAAQ,EAAE;MAC3B;MACA,IAAI,CAACA,QAAO,IAAK,OAAOA,QAAQ,CAAC0C,CAAA,KAAM,QAAO,IAAK,OAAO1C,QAAQ,CAACiE,CAAA,KAAM,QAAO,IAAK,OAAOjE,QAAQ,CAACkF,CAAA,KAAM,QAAQ,EAAE;QACnHJ,OAAO,CAACK,KAAK,CAAC,2CAA2C,EAAEnF,QAAQ;QACnEA,QAAO,GAAI,IAAIjC,KAAK,CAAC0G,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACtC;MAEA,IAAIW,MAAK;MACT,MAAMC,EAAC,GAAI,IAAI,CAACtG,MAAM,EAAC;MACvB,MAAMT,IAAG,GAAI,GAAG,IAAI,CAACgH,iBAAiB,CAAC/E,IAAI,CAAC,GAAG8E,EAAE,EAAC;MAElD,IAAI;QACF,QAAQ9E,IAAI;UACV,KAAK,QAAQ;YACX6E,MAAK,GAAI/G,0BAA0B,CAAC,IAAIJ,MAAM,CAACoH,EAAE,EAAE/G,IAAI,EAAE0B,QAAQ,CAAC;YAClE;UACF;YACE8E,OAAO,CAACC,IAAI,CAAC,oCAAoC,EAAExE,IAAI;YACvD;QACJ;QAEA,IAAI,CAAC6E,MAAK,IAAK,CAACA,MAAM,CAACG,IAAI,EAAE;UAC3BT,OAAO,CAACK,KAAK,CAAC,+CAA+C;UAC7D;QACF;QAEA,IAAI,CAAC3G,KAAK,CAACgD,GAAG,CAAC4D,MAAM,CAACG,IAAI;QAC1B,IAAI,CAAC1G,YAAY,CAAC2G,IAAI,CAACJ,MAAM;QAC7B,IAAI,CAACtG,cAAa,GAAIsG,MAAK;;QAE3B;MACF,EAAE,OAAOD,KAAK,EAAE;QACdL,OAAO,CAACK,KAAK,CAAC,kBAAkB5E,IAAI,UAAU,EAAE4E,KAAK;MACvD;IACF,CAAC;IAEDM,YAAYA,CAACL,MAAM,EAAE;MACnB;MACA,IAAI,IAAI,CAACtG,cAAa,IAAK,IAAI,CAACA,cAAc,CAAC4G,WAAW,EAAE;QAC1D,IAAI,CAAC5G,cAAc,CAAC4G,WAAW,CAAC,KAAK;MACvC;MAEA,IAAI,CAAC5G,cAAa,GAAIsG,MAAK;;MAE3B;MACA,IAAIA,MAAK,IAAKA,MAAM,CAACM,WAAW,EAAE;QAChCN,MAAM,CAACM,WAAW,CAAC,IAAI;MACzB;;MAEA;MACA,IAAI,CAACC,YAAY,CAAC;IACpB,CAAC;IAEDC,YAAYA,CAACP,EAAE,EAAE;MACf,MAAMQ,KAAI,GAAI,IAAI,CAAChH,YAAY,CAACiH,SAAS,CAACC,GAAE,IAAKA,GAAG,CAACV,EAAC,KAAMA,EAAE;MAC9D,IAAIQ,KAAI,KAAM,CAAC,CAAC,EAAE;QAChB,MAAMT,MAAK,GAAI,IAAI,CAACvG,YAAY,CAACgH,KAAK;QACtC,IAAI,CAACrH,KAAK,CAACwH,MAAM,CAACZ,MAAM,CAACG,IAAI;QAC7B,IAAI,CAAC1G,YAAY,CAACoH,MAAM,CAACJ,KAAK,EAAE,CAAC;QAEjC,IAAI,IAAI,CAAC/G,cAAa,IAAK,IAAI,CAACA,cAAc,CAACuG,EAAC,KAAMA,EAAE,EAAE;UACxD,IAAI,CAACvG,cAAa,GAAI,IAAG;QAC3B;MACF;IACF,CAAC;IAEDoH,oBAAoBA,CAACC,QAAQ,EAAEC,KAAK,EAAE;MACpC,IAAI,IAAI,CAACtH,cAAa,IAAK,IAAI,CAACA,cAAc,CAACuH,cAAc,EAAE;QAC7D,IAAI,CAACvH,cAAc,CAACuH,cAAc,CAACF,QAAQ,EAAEC,KAAK;MACpD;IACF,CAAC;IAEDE,oBAAoBA,CAACC,IAAI,EAAEH,KAAK,EAAE;MAChC,IAAI,IAAI,CAACtH,cAAa,IAAK,IAAI,CAACA,cAAc,CAACkB,QAAQ,EAAE;QACvD,IAAI,CAAClB,cAAc,CAACkB,QAAQ,CAACuG,IAAI,IAAIH,KAAI;;QAEzC;QACA,IAAI,IAAI,CAACtH,cAAc,CAACyB,IAAG,KAAM,MAAM,EAAE;UACvC;UACA,IAAI,IAAI,CAACzB,cAAc,CAAC0H,MAAK,IAAK,IAAI,CAAC1H,cAAc,CAAC0H,MAAM,CAACC,MAAK,GAAI,CAAC,EAAE;YACvE,IAAI,CAAC3H,cAAc,CAAC0H,MAAM,CAAC,CAAC,CAAC,CAACD,IAAI,IAAIH,KAAI;YAC1C;YACA,IAAI,CAACtH,cAAc,CAAC4H,cAAc,CAAC;YACnC,IAAI,CAAC5H,cAAc,CAAC6H,mBAAmB,CAAC;UAC1C;QACF,OAAO;UACL;UACA,IAAI,IAAI,CAAC7H,cAAc,CAACyG,IAAI,EAAE;YAC5B,IAAI,CAACzG,cAAc,CAACyG,IAAI,CAACvF,QAAQ,CAACuG,IAAI,IAAIH,KAAI;UAChD;QACF;MACF;IACF,CAAC;IAEDQ,aAAaA,CAACrG,IAAI,EAAE;MAClB,MAAMsG,KAAI,GAAI;QACZC,MAAM,EAAE;MACV;MACA,OAAOD,KAAK,CAACtG,IAAI,KAAK,GAAE;IAC1B,CAAC;IAED+E,iBAAiBA,CAAC/E,IAAI,EAAE;MACtB,MAAMwG,KAAI,GAAI;QACZD,MAAM,EAAE;MACV;MACA,OAAOC,KAAK,CAACxG,IAAI,KAAK,IAAG;IAC3B,CAAC;IAEDc,aAAaA,CAACoC,KAAK,EAAE;MACnB,MAAME,IAAG,GAAI,IAAI,CAAChE,KAAK,CAACC,cAAc,CAACgE,qBAAqB,CAAC;MAC7D,MAAMC,KAAI,GAAI,IAAI9F,KAAK,CAAC+F,OAAO,CAAC;MAChCD,KAAK,CAACnB,CAAA,GAAK,CAACe,KAAK,CAACM,OAAM,GAAIJ,IAAI,CAACK,IAAI,IAAIL,IAAI,CAAC7B,KAAK,GAAI,IAAI;MAC3D+B,KAAK,CAACI,CAAA,GAAI,EAAE,CAACR,KAAK,CAACS,OAAM,GAAIP,IAAI,CAACQ,GAAG,IAAIR,IAAI,CAAC5B,MAAM,IAAI,IAAI;MAE5D,MAAMqC,SAAQ,GAAI,IAAIrG,KAAK,CAACsG,SAAS,CAAC;MACtCD,SAAS,CAACE,aAAa,CAACT,KAAK,EAAE,IAAI,CAACpF,MAAM;;MAE1C;MACA,MAAMuI,UAAS,GAAI5C,SAAS,CAAC6C,gBAAgB,CAC3C,IAAI,CAACpI,YAAY,CAACqI,GAAG,CAACnB,GAAE,IAAKA,GAAG,CAACR,IAAI,CAAC,CAAC4B,MAAM,CAAC5B,IAAG,IAAKA,IAAI,CAAC,EAC3D,IAAG,CAAE;MACP;MAEA,IAAIyB,UAAU,CAACP,MAAK,GAAI,CAAC,EAAE;QACzB,MAAMW,WAAU,GAAIJ,UAAU,CAAC,CAAC,CAAC,CAAC5B,MAAK;;QAEvC;QACA,IAAIiC,aAAY,GAAI,IAAG;;QAEvB;QACAA,aAAY,GAAI,IAAI,CAACxI,YAAY,CAACyI,IAAI,CAACvB,GAAE,IAAKA,GAAG,CAACR,IAAG,KAAM6B,WAAW;;QAEtE;QACA,IAAI,CAACC,aAAa,EAAE;UAClB,IAAIE,MAAK,GAAIH,WAAW,CAACG,MAAK;UAC9B,OAAOA,MAAK,IAAK,CAACF,aAAa,EAAE;YAC/BA,aAAY,GAAI,IAAI,CAACxI,YAAY,CAACyI,IAAI,CAACvB,GAAE,IAAKA,GAAG,CAACR,IAAG,KAAMgC,MAAM;YACjEA,MAAK,GAAIA,MAAM,CAACA,MAAK;UACvB;QACF;;QAEA;QACA,IAAI,CAACF,aAAa,EAAE;UAClB;UACA,IAAID,WAAW,CAACI,QAAO,IAAKJ,WAAW,CAACI,QAAQ,CAACpE,UAAS,IAAKgE,WAAW,CAACI,QAAQ,CAACC,QAAQ,EAAE;YAC5F,MAAM;cAAErE,UAAU;cAAEqE;YAAS,IAAIL,WAAW,CAACI,QAAO;YACpDH,aAAY,GAAI,IAAI,CAACxI,YAAY,CAACyI,IAAI,CAACvB,GAAE,IACvCA,GAAG,CAACxF,IAAG,KAAM6C,UAAS,IAAK2C,GAAG,CAACV,EAAC,KAAMoC,QACxC;UACF;;UAEA;UACA,IAAI,CAACJ,aAAY,IAAKD,WAAW,CAACG,MAAK,IAAKH,WAAW,CAACG,MAAM,CAACC,QAAQ,EAAE;YACvE,MAAM;cAAEpE,UAAU;cAAEqE;YAAS,IAAIL,WAAW,CAACG,MAAM,CAACC,QAAO;YAC3D,IAAIpE,UAAS,IAAKqE,QAAQ,EAAE;cAC1BJ,aAAY,GAAI,IAAI,CAACxI,YAAY,CAACyI,IAAI,CAACvB,GAAE,IACvCA,GAAG,CAACxF,IAAG,KAAM6C,UAAS,IAAK2C,GAAG,CAACV,EAAC,KAAMoC,QACxC;YACF;UACF;QACF;QAEA,IAAIJ,aAAa,EAAE;UACjB,IAAI,CAAC5B,YAAY,CAAC4B,aAAa;QACjC,OAAO;UACL,IAAI,CAAC5B,YAAY,CAAC,IAAI;QACxB;MACF,OAAO;QACL,IAAI,CAACA,YAAY,CAAC,IAAI;MACxB;IACF,CAAC;IAEDrE,cAAcA,CAAA,EAAG;MACf,MAAM1B,SAAQ,GAAI,IAAI,CAACC,KAAK,CAACC,cAAa;MAC1C,IAAI,CAACnB,MAAM,CAACiJ,MAAK,GAAIhI,SAAS,CAACI,WAAU,GAAIJ,SAAS,CAACK,YAAW;MAClE,IAAI,CAACtB,MAAM,CAACkJ,sBAAsB,CAAC;MACnC,IAAI,CAACjJ,QAAQ,CAAC0B,OAAO,CAACV,SAAS,CAACI,WAAW,EAAEJ,SAAS,CAACK,YAAY;IACrE,CAAC;IAED;IACAZ,OAAOA,CAAA,EAAG;MACRyI,qBAAqB,CAAC,IAAI,CAACzI,OAAO;;MAElC;MACA,IAAI,CAACR,QAAQ,CAACkJ,MAAM,CAAC;;MAErB;MACA,IAAI,CAAChJ,YAAY,CAACiJ,OAAO,CAAC/B,GAAE,IAAK;QAC/B,IAAIA,GAAG,CAAC8B,MAAM,EAAE;UACd9B,GAAG,CAAC8B,MAAM,CAAC;QACb;MACF,CAAC;;MAED;MACA,IAAI,CAACnJ,QAAQ,CAACqJ,MAAM,CAAC,IAAI,CAACvJ,KAAK,EAAE,IAAI,CAACC,MAAM;IAC9C,CAAC;IAID;IACAuJ,uBAAuBA,CAAA,EAAG;MACxB,IAAI,CAAC,IAAI,CAAClJ,cAAc,EAAE;MAE1B,MAAMmJ,MAAK,GAAI,IAAIlK,KAAK,CAAC0G,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC;MACxC,MAAMyD,WAAU,GAAI,IAAI,CAACpJ,cAAc,CAACkB,QAAQ,CAACmI,KAAK,CAAC,CAAC,CAAC3G,GAAG,CAACyG,MAAM;MAEnE,QAAQ,IAAI,CAACnJ,cAAc,CAACyB,IAAI;QAC9B,KAAK,QAAQ;UAAE;YACb,IAAI,CAAC0E,YAAY,CAAC,QAAQ,EAAEiD,WAAW;YACvC;UACF;MACF;IACF,CAAC;IAED;IACAE,UAAUA,CAAA,EAAG;MACX,IAAIC,OAAO,CAAC,qBAAqB,CAAC,EAAE;QAClC;QACA,IAAI,CAACxJ,YAAY,CAACiJ,OAAO,CAAC/B,GAAE,IAAK;UAC/B,IAAI,CAACvH,KAAK,CAACwH,MAAM,CAACD,GAAG,CAACR,IAAI;UAC1B,IAAIQ,GAAG,CAAC1G,OAAO,EAAE;YACf0G,GAAG,CAAC1G,OAAO,CAAC;UACd;QACF,CAAC;QAED,IAAI,CAACR,YAAW,GAAI,EAAC;QACrB,IAAI,CAACC,cAAa,GAAI,IAAG;QACzB,IAAI,CAACC,MAAK,GAAI;MAChB;IACF,CAAC;IAED;IACAkC,6BAA6BA,CAACmC,UAAU,EAAEqE,QAAQ,EAAES,WAAW,EAAE;MAC/D;MACA,MAAM9C,MAAK,GAAI,IAAI,CAACvG,YAAY,CAACyI,IAAI,CAACvB,GAAE,IAAKA,GAAG,CAACV,EAAC,KAAMoC,QAAO,IAAK1B,GAAG,CAACxF,IAAG,KAAM6C,UAAU;MAC3F,IAAI,CAACgC,MAAM,EAAE;QACX;MACF;;MAEA;MACAA,MAAM,CAACpF,QAAQ,CAACsI,IAAI,CAACJ,WAAW;;MAEhC;MACA,QAAQ9E,UAAU;QAChB,KAAK,QAAQ;UACX;UACA,IAAIgC,MAAM,CAACG,IAAI,EAAE;YACfH,MAAM,CAACG,IAAI,CAACvF,QAAQ,CAACsI,IAAI,CAACJ,WAAW;UACvC;UACA;MACJ;;MAEA;MACA,IAAI,IAAI,CAACpJ,cAAa,IAAK,IAAI,CAACA,cAAc,CAACuG,EAAC,KAAMoC,QAAQ,EAAE;QAC9D;QACA,IAAI,CAAC9B,YAAY,CAAC;MACpB;IACF;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}