# 地图场景导出导入功能说明

## 功能概述

本应用现在支持将3D地图场景数据导出为JSON格式，以及从JSON文件导入场景数据。

## 使用方法

### 导出场景

1. 在3D场景中创建一些对象（如机器人）
2. 点击右侧控制面板中的"导出场景"按钮
3. 系统会自动下载一个JSON文件，文件名格式为：`scene_YYYY-MM-DDTHH-mm-ss.json`

### 导入场景

1. 点击右侧控制面板中的"导入场景"按钮
2. 选择之前导出的JSON文件或符合格式的场景文件
3. 如果当前场景中有对象，系统会询问是否要清空当前场景
4. 确认后，系统会根据JSON数据重建场景

## JSON数据格式

导出的JSON文件包含以下结构：

```json
{
  "version": "1.0",
  "timestamp": "2025-08-05T07:40:00.000Z",
  "objects": [
    {
      "id": 1,
      "name": "机器人1",
      "type": "person",
      "position": {
        "x": 2,
        "y": 0,
        "z": 3
      },
      "rotation": 0
    }
  ],
  "nextId": 4
}
```

### 字段说明

- `version`: 数据格式版本号
- `timestamp`: 导出时间戳
- `objects`: 场景中的所有对象数组
  - `id`: 对象唯一标识符
  - `name`: 对象名称
  - `type`: 对象类型（目前支持 "person"）
  - `position`: 对象3D位置坐标
    - `x`: X轴坐标
    - `y`: Y轴坐标（通常为0，表示在地面上）
    - `z`: Z轴坐标
  - `rotation`: 对象朝向角度（弧度制）
- `nextId`: 下一个新对象的ID

## 测试示例

项目中包含了一个示例场景文件 `example-scene.json`，您可以使用它来测试导入功能：

1. 点击"导入场景"按钮
2. 选择 `example-scene.json` 文件
3. 场景中会出现3个机器人，分别位于不同位置和朝向

## 注意事项

1. 导入场景会清空当前场景中的所有对象
2. 只支持JSON格式的场景文件
3. 导入的文件必须符合指定的数据格式
4. 如果导入过程中出现错误，请检查浏览器控制台的错误信息
5. 导出的文件会自动保存到浏览器的默认下载目录

## 扩展性

当前版本支持人物对象的导出导入。未来可以扩展支持更多对象类型，只需要：

1. 在 `serializeObject` 方法中添加新对象类型的序列化逻辑
2. 在 `createObjectFromData` 方法中添加新对象类型的反序列化逻辑
3. 确保新对象类型的类实现了必要的接口方法
