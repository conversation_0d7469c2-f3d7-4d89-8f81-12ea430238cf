{"ast": null, "code": "import { createElementVNode as _createElementVNode, renderList as _renderList, Fragment as _Fragment, openBlock as _openBlock, createElementBlock as _createElementBlock, toDisplayString as _toDisplayString, withModifiers as _withModifiers, normalizeClass as _normalizeClass, vModelText as _vModelText, withDirectives as _withDirectives, createCommentVNode as _createCommentVNode } from \"vue\";\nconst _hoisted_1 = {\n  class: \"map3d-container\"\n};\nconst _hoisted_2 = {\n  ref: \"threeContainer\",\n  class: \"three-container\"\n};\nconst _hoisted_3 = {\n  class: \"control-panel\"\n};\nconst _hoisted_4 = {\n  class: \"drag-items\"\n};\nconst _hoisted_5 = {\n  class: \"object-list\"\n};\nconst _hoisted_6 = [\"onClick\"];\nconst _hoisted_7 = [\"onClick\"];\nconst _hoisted_8 = {\n  key: 0,\n  class: \"object-properties\"\n};\nconst _hoisted_9 = {\n  class: \"property-group\"\n};\nconst _hoisted_10 = {\n  class: \"property-group\"\n};\nconst _hoisted_11 = [\"value\"];\nconst _hoisted_12 = {\n  class: \"property-group\"\n};\nconst _hoisted_13 = [\"value\"];\nconst _hoisted_14 = {\n  class: \"property-group\"\n};\nconst _hoisted_15 = [\"value\"];\nconst _hoisted_16 = {\n  key: 0,\n  class: \"property-group\"\n};\nconst _hoisted_17 = [\"value\"];\nconst _hoisted_18 = {\n  key: 1,\n  class: \"property-group\"\n};\nconst _hoisted_19 = [\"value\"];\nconst _hoisted_20 = {\n  key: 2,\n  class: \"property-group\"\n};\nconst _hoisted_21 = [\"value\"];\nconst _hoisted_22 = {\n  key: 3,\n  class: \"property-group\"\n};\nconst _hoisted_23 = [\"value\"];\nconst _hoisted_24 = {\n  class: \"action-buttons\"\n};\nexport function render(_ctx, _cache, $props, $setup, $data, $options) {\n  return _openBlock(), _createElementBlock(\"div\", _hoisted_1, [_createElementVNode(\"div\", _hoisted_2, null, 512 /* NEED_PATCH */), _createElementVNode(\"div\", _hoisted_3, [_createElementVNode(\"div\", _hoisted_4, [_cache[15] || (_cache[15] = _createElementVNode(\"h3\", null, \"拖拽元素\", -1 /* CACHED */)), _createElementVNode(\"div\", {\n    class: \"drag-item\",\n    draggable: \"true\",\n    onDragstart: _cache[0] || (_cache[0] = $event => $options.startDrag('person', $event))\n  }, _cache[13] || (_cache[13] = [_createElementVNode(\"span\", null, \"👤 人物\", -1 /* CACHED */)]), 32 /* NEED_HYDRATION */), _createElementVNode(\"div\", {\n    class: \"drag-item\",\n    draggable: \"true\",\n    onDragstart: _cache[1] || (_cache[1] = $event => $options.startDrag('obstacle', $event))\n  }, _cache[14] || (_cache[14] = [_createElementVNode(\"span\", null, \"🧱 障碍物\", -1 /* CACHED */)]), 32 /* NEED_HYDRATION */)]), _createElementVNode(\"div\", _hoisted_5, [_cache[16] || (_cache[16] = _createElementVNode(\"h3\", null, \"场景对象\", -1 /* CACHED */)), (_openBlock(true), _createElementBlock(_Fragment, null, _renderList($data.sceneObjects, obj => {\n    return _openBlock(), _createElementBlock(\"div\", {\n      key: obj.id,\n      class: _normalizeClass([\"object-item\", {\n        selected: $data.selectedObject?.id === obj.id\n      }]),\n      onClick: $event => $options.selectObject(obj)\n    }, [_createElementVNode(\"span\", null, _toDisplayString($options.getObjectIcon(obj.type)) + \" \" + _toDisplayString(obj.name), 1 /* TEXT */), _createElementVNode(\"button\", {\n      onClick: _withModifiers($event => $options.deleteObject(obj.id), [\"stop\"]),\n      class: \"delete-btn\"\n    }, \"×\", 8 /* PROPS */, _hoisted_7)], 10 /* CLASS, PROPS */, _hoisted_6);\n  }), 128 /* KEYED_FRAGMENT */))]), $data.selectedObject ? (_openBlock(), _createElementBlock(\"div\", _hoisted_8, [_cache[26] || (_cache[26] = _createElementVNode(\"h3\", null, \"属性编辑\", -1 /* CACHED */)), _createElementVNode(\"div\", _hoisted_9, [_cache[17] || (_cache[17] = _createElementVNode(\"label\", null, \"名称:\", -1 /* CACHED */)), _withDirectives(_createElementVNode(\"input\", {\n    \"onUpdate:modelValue\": _cache[2] || (_cache[2] = $event => $data.selectedObject.name = $event),\n    onInput: _cache[3] || (_cache[3] = $event => $options.updateObjectProperty('name', $event.target.value))\n  }, null, 544 /* NEED_HYDRATION, NEED_PATCH */), [[_vModelText, $data.selectedObject.name]])]), _createElementVNode(\"div\", _hoisted_10, [_cache[18] || (_cache[18] = _createElementVNode(\"label\", null, \"X坐标:\", -1 /* CACHED */)), _createElementVNode(\"input\", {\n    type: \"number\",\n    value: $data.selectedObject.position?.x || 0,\n    onInput: _cache[4] || (_cache[4] = $event => $options.updateObjectPosition('x', parseFloat($event.target.value)))\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_11)]), _createElementVNode(\"div\", _hoisted_12, [_cache[19] || (_cache[19] = _createElementVNode(\"label\", null, \"Y坐标:\", -1 /* CACHED */)), _createElementVNode(\"input\", {\n    type: \"number\",\n    value: $data.selectedObject.position?.y || 0,\n    onInput: _cache[5] || (_cache[5] = $event => $options.updateObjectPosition('y', parseFloat($event.target.value)))\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_13)]), _createElementVNode(\"div\", _hoisted_14, [_cache[20] || (_cache[20] = _createElementVNode(\"label\", null, \"Z坐标:\", -1 /* CACHED */)), _createElementVNode(\"input\", {\n    type: \"number\",\n    value: $data.selectedObject.position?.z || 0,\n    onInput: _cache[6] || (_cache[6] = $event => $options.updateObjectPosition('z', parseFloat($event.target.value)))\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_15)]), _createCommentVNode(\" 人物特有属性 \"), $data.selectedObject.type === 'person' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_16, [_cache[21] || (_cache[21] = _createElementVNode(\"label\", null, \"朝向角度:\", -1 /* CACHED */)), _createElementVNode(\"input\", {\n    type: \"number\",\n    value: Math.round($data.selectedObject.getRotationDegrees ? $data.selectedObject.getRotationDegrees() : 0),\n    onInput: _cache[7] || (_cache[7] = $event => $options.updateObjectProperty('rotation', parseFloat($event.target.value) * Math.PI / 180)),\n    min: \"0\",\n    max: \"360\",\n    step: \"15\"\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_17), _cache[22] || (_cache[22] = _createElementVNode(\"small\", {\n    style: {\n      \"display\": \"block\",\n      \"color\": \"#666\",\n      \"margin-top\": \"5px\"\n    }\n  }, \" 0°=正面朝前, 90°=朝右, 180°=朝后, 270°=朝左 \", -1 /* CACHED */))])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 障碍物特有属性 \"), $data.selectedObject.type === 'obstacle' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_18, [_cache[23] || (_cache[23] = _createElementVNode(\"label\", null, \"宽度:\", -1 /* CACHED */)), _createElementVNode(\"input\", {\n    type: \"number\",\n    value: $data.selectedObject.width,\n    onInput: _cache[8] || (_cache[8] = $event => $options.updateObjectProperty('width', parseFloat($event.target.value)))\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_19)])) : _createCommentVNode(\"v-if\", true), $data.selectedObject.type === 'obstacle' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_20, [_cache[24] || (_cache[24] = _createElementVNode(\"label\", null, \"高度:\", -1 /* CACHED */)), _createElementVNode(\"input\", {\n    type: \"number\",\n    value: $data.selectedObject.height,\n    onInput: _cache[9] || (_cache[9] = $event => $options.updateObjectProperty('height', parseFloat($event.target.value)))\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_21)])) : _createCommentVNode(\"v-if\", true), $data.selectedObject.type === 'obstacle' ? (_openBlock(), _createElementBlock(\"div\", _hoisted_22, [_cache[25] || (_cache[25] = _createElementVNode(\"label\", null, \"深度:\", -1 /* CACHED */)), _createElementVNode(\"input\", {\n    type: \"number\",\n    value: $data.selectedObject.depth,\n    onInput: _cache[10] || (_cache[10] = $event => $options.updateObjectProperty('depth', parseFloat($event.target.value)))\n  }, null, 40 /* PROPS, NEED_HYDRATION */, _hoisted_23)])) : _createCommentVNode(\"v-if\", true)])) : _createCommentVNode(\"v-if\", true), _createCommentVNode(\" 操作按钮 \"), _createElementVNode(\"div\", _hoisted_24, [$data.selectedObject ? (_openBlock(), _createElementBlock(\"button\", {\n    key: 0,\n    onClick: _cache[11] || (_cache[11] = (...args) => $options.duplicateSelectedObject && $options.duplicateSelectedObject(...args)),\n    class: \"action-btn\"\n  }, \" 复制对象 \")) : _createCommentVNode(\"v-if\", true), _createElementVNode(\"button\", {\n    onClick: _cache[12] || (_cache[12] = (...args) => $options.clearScene && $options.clearScene(...args)),\n    class: \"action-btn danger\"\n  }, \" 清空场景 \")])])]);\n}", "map": {"version": 3, "names": ["class", "ref", "_createElementBlock", "_hoisted_1", "_createElementVNode", "_hoisted_2", "_hoisted_3", "_hoisted_4", "draggable", "onDragstart", "_cache", "$event", "$options", "startDrag", "_hoisted_5", "_Fragment", "_renderList", "$data", "sceneObjects", "obj", "key", "id", "_normalizeClass", "selected", "selectedObject", "onClick", "selectObject", "_toDisplayString", "getObjectIcon", "type", "name", "_withModifiers", "deleteObject", "_hoisted_7", "_hoisted_8", "_hoisted_9", "onInput", "updateObjectProperty", "target", "value", "_hoisted_10", "position", "x", "updateObjectPosition", "parseFloat", "_hoisted_12", "y", "_hoisted_14", "z", "_createCommentVNode", "_hoisted_16", "Math", "round", "getRotationDegrees", "PI", "min", "max", "step", "style", "_hoisted_18", "width", "_hoisted_20", "height", "_hoisted_22", "depth", "_hoisted_24", "args", "duplicateSelectedObject", "clearScene"], "sources": ["D:\\code\\map\\my-map\\src\\components\\Map3D.vue"], "sourcesContent": ["<template>\n  <div class=\"map3d-container\">\n    <div ref=\"threeContainer\" class=\"three-container\"></div>\n    <div class=\"control-panel\">\n      <div class=\"drag-items\">\n        <h3>拖拽元素</h3>\n        <div class=\"drag-item\" draggable=\"true\" @dragstart=\"startDrag('person', $event)\">\n          <span>👤 人物</span>\n        </div>\n\n        <div class=\"drag-item\" draggable=\"true\" @dragstart=\"startDrag('obstacle', $event)\">\n          <span>🧱 障碍物</span>\n        </div>\n      </div>\n      \n      <div class=\"object-list\">\n        <h3>场景对象</h3>\n        <div v-for=\"obj in sceneObjects\" :key=\"obj.id\" class=\"object-item\" \n             :class=\"{ selected: selectedObject?.id === obj.id }\"\n             @click=\"selectObject(obj)\">\n          <span>{{ getObjectIcon(obj.type) }} {{ obj.name }}</span>\n          <button @click.stop=\"deleteObject(obj.id)\" class=\"delete-btn\">×</button>\n        </div>\n      </div>\n      \n      <div v-if=\"selectedObject\" class=\"object-properties\">\n        <h3>属性编辑</h3>\n        <div class=\"property-group\">\n          <label>名称:</label>\n          <input v-model=\"selectedObject.name\" @input=\"updateObjectProperty('name', $event.target.value)\">\n        </div>\n        <div class=\"property-group\">\n          <label>X坐标:</label>\n          <input type=\"number\" :value=\"selectedObject.position?.x || 0\"\n                 @input=\"updateObjectPosition('x', parseFloat($event.target.value))\">\n        </div>\n        <div class=\"property-group\">\n          <label>Y坐标:</label>\n          <input type=\"number\" :value=\"selectedObject.position?.y || 0\"\n                 @input=\"updateObjectPosition('y', parseFloat($event.target.value))\">\n        </div>\n        <div class=\"property-group\">\n          <label>Z坐标:</label>\n          <input type=\"number\" :value=\"selectedObject.position?.z || 0\"\n                 @input=\"updateObjectPosition('z', parseFloat($event.target.value))\">\n        </div>\n\n        <!-- 人物特有属性 -->\n        <div v-if=\"selectedObject.type === 'person'\" class=\"property-group\">\n          <label>朝向角度:</label>\n          <input type=\"number\" :value=\"Math.round(selectedObject.getRotationDegrees ? selectedObject.getRotationDegrees() : 0)\"\n                 @input=\"updateObjectProperty('rotation', parseFloat($event.target.value) * Math.PI / 180)\"\n                 min=\"0\" max=\"360\" step=\"15\">\n          <small style=\"display: block; color: #666; margin-top: 5px;\">\n            0°=正面朝前, 90°=朝右, 180°=朝后, 270°=朝左\n          </small>\n        </div>\n\n        <!-- 障碍物特有属性 -->\n        <div v-if=\"selectedObject.type === 'obstacle'\" class=\"property-group\">\n          <label>宽度:</label>\n          <input type=\"number\" :value=\"selectedObject.width\" \n                 @input=\"updateObjectProperty('width', parseFloat($event.target.value))\">\n        </div>\n        <div v-if=\"selectedObject.type === 'obstacle'\" class=\"property-group\">\n          <label>高度:</label>\n          <input type=\"number\" :value=\"selectedObject.height\" \n                 @input=\"updateObjectProperty('height', parseFloat($event.target.value))\">\n        </div>\n        <div v-if=\"selectedObject.type === 'obstacle'\" class=\"property-group\">\n          <label>深度:</label>\n          <input type=\"number\" :value=\"selectedObject.depth\" \n                 @input=\"updateObjectProperty('depth', parseFloat($event.target.value))\">\n        </div>\n      </div>\n\n      <!-- 操作按钮 -->\n      <div class=\"action-buttons\">\n        <button @click=\"duplicateSelectedObject\" class=\"action-btn\" v-if=\"selectedObject\">\n          复制对象\n        </button>\n        <button @click=\"clearScene\" class=\"action-btn danger\">\n          清空场景\n        </button>\n      </div>\n    </div>\n  </div>\n</template>\n\n<script>\nimport * as THREE from 'three'\nimport { OrbitControls } from 'three/examples/jsm/controls/OrbitControls.js'\nimport { Person } from '../classes/Person.js'\nimport { Obstacle } from '../classes/Obstacle.js'\nimport { DragManager } from '../managers/DragManager.js'\nimport { markRaw } from 'vue'\nimport { createNonReactiveThreeObject, makeThreeObjectNonReactive } from '../utils/threeUtils.js'\n\nexport default {\n  name: 'Map3D',\n  data() {\n    return {\n      scene: null,\n      camera: null,\n      renderer: null,\n      controls: null,\n      dragManager: null,\n      sceneObjects: [],\n      selectedObject: null,\n      nextId: 1\n    }\n  },\n\n  mounted() {\n    this.initThreeJS()\n    this.setupDragAndDrop()\n    // this.animate()\n  },\n  beforeUnmount() {\n    if (this.renderer) {\n      this.renderer.dispose()\n    }\n  },\n  methods: {\n    initThreeJS() {\n      // 创建场景\n      this.scene = createNonReactiveThreeObject(THREE.Scene)\n      this.scene.background = new THREE.Color(0xf0f0f0)\n\n      // 创建相机\n      const container = this.$refs.threeContainer\n      this.camera = createNonReactiveThreeObject(THREE.PerspectiveCamera,\n        75,\n        container.clientWidth / container.clientHeight,\n        0.1,\n        1000\n      )\n      this.camera.position.set(10, 10, 10)\n\n      // 创建渲染器\n      this.renderer = createNonReactiveThreeObject(THREE.WebGLRenderer, { antialias: true })\n      this.renderer.setSize(container.clientWidth, container.clientHeight)\n\n      this.renderer.shadowMap.enabled = true\n      this.renderer.shadowMap.type = THREE.PCFSoftShadowMap\n      container.appendChild(this.renderer.domElement)\n\n      // 添加控制器\n      this.controls = makeThreeObjectNonReactive(new OrbitControls(this.camera, this.renderer.domElement))\n      this.controls.enableDamping = true\n      this.controls.dampingFactor = 0.05\n      \n      // 添加光源\n      this.setupLighting()\n      \n      // 添加地面\n      this.createFloor()\n      \n      // 初始化拖拽管理器\n      this.dragManager = markRaw(new DragManager(this.scene, this.camera, this.renderer.domElement))\n      this.dragManager.setControls(this.controls)\n      this.dragManager.setObjectPositionUpdateCallback(this.onDraggedObjectPositionUpdate)\n\n      // 监听窗口大小变化\n      window.addEventListener('resize', this.onWindowResize)\n\n      // 监听点击事件\n      this.renderer.domElement.addEventListener('click', this.onCanvasClick)\n    },\n    \n    setupLighting() {\n      // 环境光\n      const ambientLight = new THREE.AmbientLight(0x404040, 0.6)\n      this.scene.add(ambientLight)\n      \n      // 方向光\n      const directionalLight = new THREE.DirectionalLight(0xffffff, 0.8)\n      directionalLight.position.set(10, 10, 5)\n      directionalLight.castShadow = true\n      directionalLight.shadow.mapSize.width = 2048\n      directionalLight.shadow.mapSize.height = 2048\n      this.scene.add(directionalLight)\n    },\n    \n    // 创建地板\n    createFloor() {\n      const geometry = new THREE.PlaneGeometry(30, 30)\n      const material = new THREE.MeshLambertMaterial({\n        color: 0xcccccc,\n        transparent: true,\n        opacity: 0.8\n      })\n      const floor = new THREE.Mesh(geometry, material)\n      floor.rotation.x = -Math.PI / 2\n      floor.receiveShadow = true\n      this.scene.add(floor)\n\n      // 添加网格\n      const gridHelper = new THREE.GridHelper(30, 30, 0x888888, 0xaaaaaa)\n      this.scene.add(gridHelper)\n\n      // 添加坐标轴辅助器（可选，用于调试）\n      const axesHelper = new THREE.AxesHelper(5)\n      this.scene.add(axesHelper)\n      // 红色=X轴(左右), 绿色=Y轴(上下), 蓝色=Z轴(前后)\n    },\n\n    setupDragAndDrop() {\n      const container = this.$refs.threeContainer\n\n      container.addEventListener('dragover', (e) => {\n        e.preventDefault()\n      })\n\n      container.addEventListener('drop', (e) => {\n        e.preventDefault()\n        const objectType = e.dataTransfer.getData('text/plain')\n        this.createObjectAtPosition(objectType, e)\n      })\n    },\n\n    startDrag(type, event) {\n      event.dataTransfer.setData('text/plain', type)\n    },\n\n    createObjectAtPosition(type, event) {\n      const rect = this.$refs.threeContainer.getBoundingClientRect()\n      const mouse = new THREE.Vector2()\n      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1\n      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1\n\n      const raycaster = new THREE.Raycaster()\n      raycaster.setFromCamera(mouse, this.camera)\n\n      // 与地面相交\n      const plane = new THREE.Plane(new THREE.Vector3(0, 1, 0), 0)\n      const intersectPoint = new THREE.Vector3()\n\n      // 检查射线与平面的交点\n      const intersection = raycaster.ray.intersectPlane(plane, intersectPoint)\n\n      // 如果没有交点，使用默认位置\n      if (!intersection) {\n        console.warn('No intersection with ground plane, using default position')\n        intersectPoint.set(0, 0, 0)\n      }\n\n      // 验证intersectPoint是有效的\n      if (!intersectPoint || typeof intersectPoint.x !== 'number' || isNaN(intersectPoint.x)) {\n        console.warn('Invalid intersect point, using default position')\n        intersectPoint.set(0, 0, 0)\n      }\n\n      this.createObject(type, intersectPoint)\n    },\n\n    createObject(type, position) {\n      // 验证position参数\n      if (!position || typeof position.x !== 'number' || typeof position.y !== 'number' || typeof position.z !== 'number') {\n        console.error('createObject: Invalid position parameter:', position)\n        position = new THREE.Vector3(0, 0, 0)\n      }\n\n      let object\n      const id = this.nextId++\n      const name = `${this.getObjectTypeName(type)}${id}`\n\n      try {\n        switch (type) {\n          case 'person':\n            object = makeThreeObjectNonReactive(new Person(id, name, position))\n            break\n          case 'obstacle':\n            object = makeThreeObjectNonReactive(new Obstacle(id, name, position, 1, 1, 1))\n            break\n          default:\n            console.warn('createObject: Unknown object type:', type)\n            return\n        }\n\n        if (!object || !object.mesh) {\n          console.error('createObject: Failed to create object or mesh')\n          return\n        }\n\n        this.scene.add(object.mesh)\n        this.sceneObjects.push(object)\n        this.selectedObject = object\n\n        // Object created successfully\n      } catch (error) {\n        console.error(`Error creating ${type} object:`, error)\n      }\n    },\n\n    selectObject(object) {\n      // 取消之前选中对象的高亮\n      if (this.selectedObject && this.selectedObject.setSelected) {\n        this.selectedObject.setSelected(false)\n      }\n\n      this.selectedObject = object\n\n      // 高亮当前选中对象\n      if (object && object.setSelected) {\n        object.setSelected(true)\n      }\n\n      // 强制更新Vue界面\n      this.$forceUpdate()\n    },\n\n    deleteObject(id) {\n      const index = this.sceneObjects.findIndex(obj => obj.id === id)\n      if (index !== -1) {\n        const object = this.sceneObjects[index]\n        this.scene.remove(object.mesh)\n        this.sceneObjects.splice(index, 1)\n\n        if (this.selectedObject && this.selectedObject.id === id) {\n          this.selectedObject = null\n        }\n      }\n    },\n\n    updateObjectProperty(property, value) {\n      if (this.selectedObject && this.selectedObject.updateProperty) {\n        this.selectedObject.updateProperty(property, value)\n      }\n    },\n\n    updateObjectPosition(axis, value) {\n      if (this.selectedObject && this.selectedObject.position) {\n        this.selectedObject.position[axis] = value\n\n        // 根据对象类型进行不同的处理\n        if (this.selectedObject.type === 'path') {\n          // 对于路径，移动第一个点\n          if (this.selectedObject.points && this.selectedObject.points.length > 0) {\n            this.selectedObject.points[0][axis] = value\n            // 重新创建路径\n            this.selectedObject.updatePathLine()\n            this.selectedObject.createControlPoints()\n          }\n        } else if (this.selectedObject.type === 'obstacle') {\n          // 对于障碍物，使用updatePosition方法\n          if (this.selectedObject.updatePosition) {\n            this.selectedObject.updatePosition(this.selectedObject.position)\n          } else if (this.selectedObject.mesh) {\n            this.selectedObject.mesh.position[axis] = value\n          }\n        } else {\n          // 对于人物和其他对象\n          if (this.selectedObject.mesh) {\n            this.selectedObject.mesh.position[axis] = value\n          }\n        }\n      }\n    },\n\n    getObjectIcon(type) {\n      const icons = {\n        person: '👤',\n        obstacle: '🧱'\n      }\n      return icons[type] || '❓'\n    },\n\n    getObjectTypeName(type) {\n      const names = {\n        person: '人物',\n        obstacle: '障碍物'\n      }\n      return names[type] || '对象'\n    },\n\n    onCanvasClick(event) {\n      const rect = this.$refs.threeContainer.getBoundingClientRect()\n      const mouse = new THREE.Vector2()\n      mouse.x = ((event.clientX - rect.left) / rect.width) * 2 - 1\n      mouse.y = -((event.clientY - rect.top) / rect.height) * 2 + 1\n\n      const raycaster = new THREE.Raycaster()\n      raycaster.setFromCamera(mouse, this.camera)\n\n      // 递归检测所有子对象，包括Group内部的mesh\n      const intersects = raycaster.intersectObjects(\n        this.sceneObjects.map(obj => obj.mesh).filter(mesh => mesh),\n        true // 递归检测子对象\n      )\n\n      if (intersects.length > 0) {\n        const clickedMesh = intersects[0].object\n\n        // 查找对应的场景对象，需要考虑Group结构\n        let clickedObject = null\n\n        // 首先尝试直接匹配\n        clickedObject = this.sceneObjects.find(obj => obj.mesh === clickedMesh)\n\n        // 如果没找到，可能是Group内部的子对象，向上查找\n        if (!clickedObject) {\n          let parent = clickedMesh.parent\n          while (parent && !clickedObject) {\n            clickedObject = this.sceneObjects.find(obj => obj.mesh === parent)\n            parent = parent.parent\n          }\n        }\n\n        // 还可以通过userData查找\n        if (!clickedObject) {\n          // 检查点击的mesh的userData\n          if (clickedMesh.userData && clickedMesh.userData.objectType && clickedMesh.userData.objectId) {\n            const { objectType, objectId } = clickedMesh.userData\n            clickedObject = this.sceneObjects.find(obj =>\n              obj.type === objectType && obj.id === objectId\n            )\n          }\n\n          // 检查父级的userData\n          if (!clickedObject && clickedMesh.parent && clickedMesh.parent.userData) {\n            const { objectType, objectId } = clickedMesh.parent.userData\n            if (objectType && objectId) {\n              clickedObject = this.sceneObjects.find(obj =>\n                obj.type === objectType && obj.id === objectId\n              )\n            }\n          }\n        }\n\n        if (clickedObject) {\n          this.selectObject(clickedObject)\n        } else {\n          this.selectObject(null)\n        }\n      } else {\n        this.selectObject(null)\n      }\n    },\n\n    onWindowResize() {\n      const container = this.$refs.threeContainer\n      this.camera.aspect = container.clientWidth / container.clientHeight\n      this.camera.updateProjectionMatrix()\n      this.renderer.setSize(container.clientWidth, container.clientHeight)\n    },\n\n    animate() {\n      requestAnimationFrame(this.animate)\n\n      this.controls.update()\n\n      // 更新所有对象\n      this.sceneObjects.forEach(obj => {\n        if (obj.update) {\n          obj.update()\n        }\n      })\n\n      this.renderer.render(this.scene, this.camera)\n    },\n\n\n\n    // 复制对象\n    duplicateSelectedObject() {\n      if (!this.selectedObject) return\n\n      const offset = new THREE.Vector3(1, 0, 1)\n      const newPosition = this.selectedObject.position.clone().add(offset)\n\n      switch (this.selectedObject.type) {\n        case 'person': {\n          this.createObject('person', newPosition)\n          break\n        }\n        case 'obstacle': {\n          const obstacle = this.selectedObject\n          const newObstacle = makeThreeObjectNonReactive(new Obstacle(\n            this.nextId++,\n            `${obstacle.name}_copy`,\n            newPosition,\n            obstacle.width,\n            obstacle.height,\n            obstacle.depth\n          ))\n          this.scene.add(newObstacle.mesh)\n          this.sceneObjects.push(newObstacle)\n          this.selectedObject = newObstacle\n          break\n        }\n\n      }\n    },\n\n    // 清空场景\n    clearScene() {\n      if (confirm('确定要清空整个场景吗？此操作不可撤销。')) {\n        // 移除所有对象\n        this.sceneObjects.forEach(obj => {\n          this.scene.remove(obj.mesh)\n          if (obj.dispose) {\n            obj.dispose()\n          }\n        })\n\n        this.sceneObjects = []\n        this.selectedObject = null\n        this.nextId = 1\n      }\n    },\n\n    // 拖拽对象位置更新回调\n    onDraggedObjectPositionUpdate(objectType, objectId, newPosition) {\n      // 找到对应的对象\n      const object = this.sceneObjects.find(obj => obj.id === objectId && obj.type === objectType)\n      if (!object) {\n        return\n      }\n\n      // 更新对象的内部位置\n      object.position.copy(newPosition)\n\n      // 根据对象类型进行特殊处理\n      switch (objectType) {\n        case 'person':\n          // 人物对象：确保mesh位置同步\n          if (object.mesh) {\n            object.mesh.position.copy(newPosition)\n          }\n          break\n\n\n\n        case 'obstacle':\n          // 障碍物对象：使用其updatePosition方法\n          if (object.updatePosition) {\n            object.updatePosition(newPosition)\n          } else {\n            // 备用方案：直接移动mesh\n            if (object.mesh) {\n              object.mesh.position.copy(newPosition)\n            }\n          }\n          break\n      }\n\n      // 如果当前选中的是这个对象，触发界面更新\n      if (this.selectedObject && this.selectedObject.id === objectId) {\n        // 强制Vue更新界面\n        this.$forceUpdate()\n      }\n    }\n  }\n}\n</script>\n\n<style scoped>\n.map3d-container {\n  display: flex;\n  height: 100vh;\n  width: 100%;\n}\n\n.three-container {\n  flex: 1;\n  position: relative;\n  overflow: hidden;\n}\n\n.control-panel {\n  width: 300px;\n  background: #f5f5f5;\n  border-left: 1px solid #ddd;\n  padding: 20px;\n  overflow-y: auto;\n}\n\n.control-panel h3 {\n  margin: 0 0 15px 0;\n  color: #333;\n  font-size: 16px;\n  border-bottom: 2px solid #007bff;\n  padding-bottom: 5px;\n}\n\n.drag-items {\n  margin-bottom: 30px;\n}\n\n.drag-item {\n  background: #fff;\n  border: 2px dashed #007bff;\n  border-radius: 8px;\n  padding: 15px;\n  margin-bottom: 10px;\n  cursor: grab;\n  text-align: center;\n  transition: all 0.3s ease;\n}\n\n.drag-item:hover {\n  background: #e3f2fd;\n  border-color: #0056b3;\n  transform: translateY(-2px);\n}\n\n.drag-item:active {\n  cursor: grabbing;\n}\n\n.drag-item span {\n  font-size: 14px;\n  font-weight: 500;\n  color: #007bff;\n}\n\n.object-list {\n  margin-bottom: 30px;\n}\n\n.object-item {\n  background: #fff;\n  border: 1px solid #ddd;\n  border-radius: 6px;\n  padding: 10px;\n  margin-bottom: 8px;\n  cursor: pointer;\n  display: flex;\n  justify-content: space-between;\n  align-items: center;\n  transition: all 0.2s ease;\n}\n\n.object-item:hover {\n  background: #f8f9fa;\n  border-color: #007bff;\n}\n\n.object-item.selected {\n  background: #e3f2fd;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.delete-btn {\n  background: #dc3545;\n  color: white;\n  border: none;\n  border-radius: 50%;\n  width: 24px;\n  height: 24px;\n  cursor: pointer;\n  font-size: 16px;\n  line-height: 1;\n  transition: background-color 0.2s ease;\n}\n\n.delete-btn:hover {\n  background: #c82333;\n}\n\n.object-properties {\n  background: #fff;\n  border: 1px solid #ddd;\n  border-radius: 8px;\n  padding: 15px;\n}\n\n.property-group {\n  margin-bottom: 15px;\n}\n\n.property-group label {\n  display: block;\n  margin-bottom: 5px;\n  font-weight: 500;\n  color: #555;\n  font-size: 14px;\n}\n\n.property-group input {\n  width: 100%;\n  padding: 8px 12px;\n  border: 1px solid #ddd;\n  border-radius: 4px;\n  font-size: 14px;\n  transition: border-color 0.2s ease;\n}\n\n.property-group input:focus {\n  outline: none;\n  border-color: #007bff;\n  box-shadow: 0 0 0 2px rgba(0, 123, 255, 0.25);\n}\n\n.property-group input[type=\"color\"] {\n  height: 40px;\n  padding: 2px;\n}\n\n.edit-btn, .add-btn, .move-btn, .stop-btn, .action-btn {\n  padding: 8px 16px;\n  border: none;\n  border-radius: 4px;\n  cursor: pointer;\n  font-size: 14px;\n  margin: 5px 5px 5px 0;\n  transition: background-color 0.2s ease;\n}\n\n.edit-btn {\n  background: #17a2b8;\n  color: white;\n}\n\n.edit-btn:hover {\n  background: #138496;\n}\n\n.add-btn {\n  background: #28a745;\n  color: white;\n}\n\n.add-btn:hover {\n  background: #218838;\n}\n\n.move-btn {\n  background: #007bff;\n  color: white;\n}\n\n.move-btn:hover {\n  background: #0056b3;\n}\n\n.move-btn:disabled {\n  background: #6c757d;\n  cursor: not-allowed;\n}\n\n.stop-btn {\n  background: #dc3545;\n  color: white;\n}\n\n.stop-btn:hover {\n  background: #c82333;\n}\n\n.action-buttons {\n  margin-top: 20px;\n  padding-top: 20px;\n  border-top: 1px solid #ddd;\n}\n\n.action-btn {\n  background: #6c757d;\n  color: white;\n  width: 100%;\n  margin-bottom: 10px;\n}\n\n.action-btn:hover {\n  background: #545b62;\n}\n\n.action-btn.danger {\n  background: #dc3545;\n}\n\n.action-btn.danger:hover {\n  background: #c82333;\n}\n</style>\n"], "mappings": ";;EACOA,KAAK,EAAC;AAAiB;;EACrBC,GAAG,EAAC,gBAAgB;EAACD,KAAK,EAAC;;;EAC3BA,KAAK,EAAC;AAAe;;EACnBA,KAAK,EAAC;AAAY;;EAWlBA,KAAK,EAAC;AAAa;;;;;EAUGA,KAAK,EAAC;;;EAE1BA,KAAK,EAAC;AAAgB;;EAItBA,KAAK,EAAC;AAAgB;;;EAKtBA,KAAK,EAAC;AAAgB;;;EAKtBA,KAAK,EAAC;AAAgB;;;;EAOkBA,KAAK,EAAC;;;;;EAWJA,KAAK,EAAC;;;;;EAKNA,KAAK,EAAC;;;;;EAKNA,KAAK,EAAC;;;;EAQlDA,KAAK,EAAC;AAAgB;;uBA5E/BE,mBAAA,CAqFM,OArFNC,UAqFM,GApFJC,mBAAA,CAAwD,OAAxDC,UAAwD,+BACxDD,mBAAA,CAkFM,OAlFNE,UAkFM,GAjFJF,mBAAA,CASM,OATNG,UASM,G,4BARJH,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAEM;IAFDJ,KAAK,EAAC,WAAW;IAACQ,SAAS,EAAC,MAAM;IAAEC,WAAS,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,SAAS,WAAWF,MAAM;kCAC5EP,mBAAA,CAAkB,cAAZ,OAAK,mB,6BAGbA,mBAAA,CAEM;IAFDJ,KAAK,EAAC,WAAW;IAACQ,SAAS,EAAC,MAAM;IAAEC,WAAS,EAAAC,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAC,SAAS,aAAaF,MAAM;kCAC9EP,mBAAA,CAAmB,cAAb,QAAM,mB,+BAIhBA,mBAAA,CAQM,OARNU,UAQM,G,4BAPJV,mBAAA,CAAa,YAAT,MAAI,sB,kBACRF,mBAAA,CAKMa,SAAA,QAAAC,WAAA,CALaC,KAAA,CAAAC,YAAY,EAAnBC,GAAG;yBAAfjB,mBAAA,CAKM;MAL4BkB,GAAG,EAAED,GAAG,CAACE,EAAE;MAAErB,KAAK,EAAAsB,eAAA,EAAC,aAAa;QAAAC,QAAA,EACzCN,KAAA,CAAAO,cAAc,EAAEH,EAAE,KAAKF,GAAG,CAACE;MAAE;MAChDI,OAAK,EAAAd,MAAA,IAAEC,QAAA,CAAAc,YAAY,CAACP,GAAG;QAC3Bf,mBAAA,CAAyD,cAAAuB,gBAAA,CAAhDf,QAAA,CAAAgB,aAAa,CAACT,GAAG,CAACU,IAAI,KAAI,GAAC,GAAAF,gBAAA,CAAGR,GAAG,CAACW,IAAI,kBAC/C1B,mBAAA,CAAwE;MAA/DqB,OAAK,EAAAM,cAAA,CAAApB,MAAA,IAAOC,QAAA,CAAAoB,YAAY,CAACb,GAAG,CAACE,EAAE;MAAGrB,KAAK,EAAC;OAAa,GAAC,iBAAAiC,UAAA,E;oCAIxDhB,KAAA,CAAAO,cAAc,I,cAAzBtB,mBAAA,CAiDM,OAjDNgC,UAiDM,G,4BAhDJ9B,mBAAA,CAAa,YAAT,MAAI,qBACRA,mBAAA,CAGM,OAHN+B,UAGM,G,4BAFJ/B,mBAAA,CAAkB,eAAX,KAAG,qB,gBACVA,mBAAA,CAAgG;+DAAhFa,KAAA,CAAAO,cAAc,CAACM,IAAI,GAAAnB,MAAA;IAAGyB,OAAK,EAAA1B,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAyB,oBAAoB,SAAS1B,MAAM,CAAC2B,MAAM,CAACC,KAAK;iEAA7EtB,KAAA,CAAAO,cAAc,CAACM,IAAI,E,KAErC1B,mBAAA,CAIM,OAJNoC,WAIM,G,4BAHJpC,mBAAA,CAAmB,eAAZ,MAAI,qBACXA,mBAAA,CAC2E;IADpEyB,IAAI,EAAC,QAAQ;IAAEU,KAAK,EAAEtB,KAAA,CAAAO,cAAc,CAACiB,QAAQ,EAAEC,CAAC;IAC/CN,OAAK,EAAA1B,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAA+B,oBAAoB,MAAMC,UAAU,CAACjC,MAAM,CAAC2B,MAAM,CAACC,KAAK;2DAEzEnC,mBAAA,CAIM,OAJNyC,WAIM,G,4BAHJzC,mBAAA,CAAmB,eAAZ,MAAI,qBACXA,mBAAA,CAC2E;IADpEyB,IAAI,EAAC,QAAQ;IAAEU,KAAK,EAAEtB,KAAA,CAAAO,cAAc,CAACiB,QAAQ,EAAEK,CAAC;IAC/CV,OAAK,EAAA1B,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAA+B,oBAAoB,MAAMC,UAAU,CAACjC,MAAM,CAAC2B,MAAM,CAACC,KAAK;2DAEzEnC,mBAAA,CAIM,OAJN2C,WAIM,G,4BAHJ3C,mBAAA,CAAmB,eAAZ,MAAI,qBACXA,mBAAA,CAC2E;IADpEyB,IAAI,EAAC,QAAQ;IAAEU,KAAK,EAAEtB,KAAA,CAAAO,cAAc,CAACiB,QAAQ,EAAEO,CAAC;IAC/CZ,OAAK,EAAA1B,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAA+B,oBAAoB,MAAMC,UAAU,CAACjC,MAAM,CAAC2B,MAAM,CAACC,KAAK;2DAGzEU,mBAAA,YAAe,EACJhC,KAAA,CAAAO,cAAc,CAACK,IAAI,iB,cAA9B3B,mBAAA,CAQM,OARNgD,WAQM,G,4BAPJ9C,mBAAA,CAAoB,eAAb,OAAK,qBACZA,mBAAA,CAEmC;IAF5ByB,IAAI,EAAC,QAAQ;IAAEU,KAAK,EAAEY,IAAI,CAACC,KAAK,CAACnC,KAAA,CAAAO,cAAc,CAAC6B,kBAAkB,GAAGpC,KAAA,CAAAO,cAAc,CAAC6B,kBAAkB;IACrGjB,OAAK,EAAA1B,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAyB,oBAAoB,aAAaO,UAAU,CAACjC,MAAM,CAAC2B,MAAM,CAACC,KAAK,IAAIY,IAAI,CAACG,EAAE;IAClFC,GAAG,EAAC,GAAG;IAACC,GAAG,EAAC,KAAK;IAACC,IAAI,EAAC;qFAC9BrD,mBAAA,CAEQ;IAFDsD,KAAqD,EAArD;MAAA;MAAA;MAAA;IAAA;EAAqD,GAAC,qCAE7D,oB,wCAGFT,mBAAA,aAAgB,EACLhC,KAAA,CAAAO,cAAc,CAACK,IAAI,mB,cAA9B3B,mBAAA,CAIM,OAJNyD,WAIM,G,4BAHJvD,mBAAA,CAAkB,eAAX,KAAG,qBACVA,mBAAA,CAC+E;IADxEyB,IAAI,EAAC,QAAQ;IAAEU,KAAK,EAAEtB,KAAA,CAAAO,cAAc,CAACoC,KAAK;IACzCxB,OAAK,EAAA1B,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAyB,oBAAoB,UAAUO,UAAU,CAACjC,MAAM,CAAC2B,MAAM,CAACC,KAAK;gGAElEtB,KAAA,CAAAO,cAAc,CAACK,IAAI,mB,cAA9B3B,mBAAA,CAIM,OAJN2D,WAIM,G,4BAHJzD,mBAAA,CAAkB,eAAX,KAAG,qBACVA,mBAAA,CACgF;IADzEyB,IAAI,EAAC,QAAQ;IAAEU,KAAK,EAAEtB,KAAA,CAAAO,cAAc,CAACsC,MAAM;IAC1C1B,OAAK,EAAA1B,MAAA,QAAAA,MAAA,MAAAC,MAAA,IAAEC,QAAA,CAAAyB,oBAAoB,WAAWO,UAAU,CAACjC,MAAM,CAAC2B,MAAM,CAACC,KAAK;gGAEnEtB,KAAA,CAAAO,cAAc,CAACK,IAAI,mB,cAA9B3B,mBAAA,CAIM,OAJN6D,WAIM,G,4BAHJ3D,mBAAA,CAAkB,eAAX,KAAG,qBACVA,mBAAA,CAC+E;IADxEyB,IAAI,EAAC,QAAQ;IAAEU,KAAK,EAAEtB,KAAA,CAAAO,cAAc,CAACwC,KAAK;IACzC5B,OAAK,EAAA1B,MAAA,SAAAA,MAAA,OAAAC,MAAA,IAAEC,QAAA,CAAAyB,oBAAoB,UAAUO,UAAU,CAACjC,MAAM,CAAC2B,MAAM,CAACC,KAAK;uIAI/EU,mBAAA,UAAa,EACb7C,mBAAA,CAOM,OAPN6D,WAOM,GAN8DhD,KAAA,CAAAO,cAAc,I,cAAhFtB,mBAAA,CAES;;IAFAuB,OAAK,EAAAf,MAAA,SAAAA,MAAA,WAAAwD,IAAA,KAAEtD,QAAA,CAAAuD,uBAAA,IAAAvD,QAAA,CAAAuD,uBAAA,IAAAD,IAAA,CAAuB;IAAElE,KAAK,EAAC;KAAmC,QAElF,K,mCACAI,mBAAA,CAES;IAFAqB,OAAK,EAAAf,MAAA,SAAAA,MAAA,WAAAwD,IAAA,KAAEtD,QAAA,CAAAwD,UAAA,IAAAxD,QAAA,CAAAwD,UAAA,IAAAF,IAAA,CAAU;IAAElE,KAAK,EAAC;KAAoB,QAEtD,E", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}