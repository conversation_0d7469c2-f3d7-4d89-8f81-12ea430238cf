{"ast": null, "code": "import \"core-js/modules/es.iterator.constructor.js\";\nimport \"core-js/modules/es.iterator.for-each.js\";\nimport \"core-js/modules/es.iterator.map.js\";\nimport * as THREE from 'three';\nexport class Person {\n  constructor(id, name, position = new THREE.Vector3(0, 0, 0)) {\n    this.id = id;\n    this.name = name;\n    this.type = 'person';\n    this.position = position.clone();\n    this.isSelected = false;\n    this.createMesh();\n    this.setupAnimation();\n  }\n  createMesh() {\n    // 创建人物的3D模型（简化版，使用基本几何体）\n    const group = new THREE.Group();\n\n    // 身体（圆柱体）\n    const bodyGeometry = new THREE.CylinderGeometry(0.3, 0.4, 1.2, 8);\n    const bodyMaterial = new THREE.MeshLambertMaterial({\n      color: 0x4a90e2\n    });\n    const body = new THREE.Mesh(bodyGeometry, bodyMaterial);\n    body.position.y = 0.6;\n    body.castShadow = true;\n    group.add(body);\n\n    // 头部（球体）\n    const headGeometry = new THREE.SphereGeometry(0.25, 8, 6);\n    const headMaterial = new THREE.MeshLambertMaterial({\n      color: 0xffdbac\n    });\n    const head = new THREE.Mesh(headGeometry, headMaterial);\n    head.position.y = 1.45;\n    head.castShadow = true;\n    group.add(head);\n\n    // 左臂\n    const armGeometry = new THREE.CylinderGeometry(0.08, 0.08, 0.8, 6);\n    const armMaterial = new THREE.MeshLambertMaterial({\n      color: 0xffdbac\n    });\n    const leftArm = new THREE.Mesh(armGeometry, armMaterial);\n    leftArm.position.set(-0.45, 0.8, 0);\n    leftArm.rotation.z = Math.PI / 6;\n    leftArm.castShadow = true;\n    group.add(leftArm);\n    const rightArm = new THREE.Mesh(armGeometry, armMaterial);\n    rightArm.position.set(0.45, 0.8, 0);\n    rightArm.rotation.z = -Math.PI / 6;\n    rightArm.castShadow = true;\n    group.add(rightArm);\n\n    // 左腿\n    const legGeometry = new THREE.CylinderGeometry(0.1, 0.1, 0.8, 6);\n    const legMaterial = new THREE.MeshLambertMaterial({\n      color: 0x2c3e50\n    });\n    const leftLeg = new THREE.Mesh(legGeometry, legMaterial);\n    leftLeg.position.set(-0.15, -0.4, 0);\n    leftLeg.castShadow = true;\n    group.add(leftLeg);\n    const rightLeg = new THREE.Mesh(legGeometry, legMaterial);\n    rightLeg.position.set(0.15, -0.4, 0);\n    rightLeg.castShadow = true;\n    group.add(rightLeg);\n\n    // 设置位置\n    group.position.copy(this.position);\n\n    // 添加用户数据\n    group.userData = {\n      draggable: true,\n      objectType: 'person',\n      objectId: this.id\n    };\n    this.mesh = group;\n    this.bodyParts = {\n      body,\n      head,\n      leftArm,\n      rightArm,\n      leftLeg,\n      rightLeg\n    };\n  }\n  setupAnimation() {\n    this.animationTime = 0;\n    this.walkCycle = {\n      armSwing: 0,\n      legSwing: 0\n    };\n  }\n  update(deltaTime = 0.016) {\n    this.animationTime += deltaTime;\n\n    // 如果正在移动，播放走路动画\n    if (this.isMoving) {\n      this.updateWalkAnimation();\n      this.updatePathMovement(deltaTime);\n    }\n\n    // 更新网格位置\n    if (this.mesh) {\n      this.mesh.position.copy(this.position);\n    }\n  }\n  updateWalkAnimation() {\n    const walkSpeed = 4;\n    const armSwingAmount = 0.3;\n    const legSwingAmount = 0.2;\n\n    // 手臂摆动\n    this.bodyParts.leftArm.rotation.x = Math.sin(this.animationTime * walkSpeed) * armSwingAmount;\n    this.bodyParts.rightArm.rotation.x = -Math.sin(this.animationTime * walkSpeed) * armSwingAmount;\n\n    // 腿部摆动\n    this.bodyParts.leftLeg.rotation.x = Math.sin(this.animationTime * walkSpeed) * legSwingAmount;\n    this.bodyParts.rightLeg.rotation.x = -Math.sin(this.animationTime * walkSpeed) * legSwingAmount;\n\n    // 身体轻微上下摆动\n    this.bodyParts.body.position.y = 0.6 + Math.sin(this.animationTime * walkSpeed * 2) * 0.05;\n  }\n  updatePathMovement(deltaTime) {\n    if (!this.currentPath || this.currentPath.length < 2) {\n      this.stopMoving();\n      return;\n    }\n    const totalDistance = this.calculatePathDistance();\n    const targetDistance = this.pathProgress * totalDistance;\n    let currentDistance = 0;\n\n    // 找到当前应该在的路径段\n    for (let i = 0; i < this.currentPath.length - 1; i++) {\n      const segmentStart = this.currentPath[i];\n      const segmentEnd = this.currentPath[i + 1];\n      const segmentDistance = segmentStart.distanceTo(segmentEnd);\n      if (currentDistance + segmentDistance >= targetDistance) {\n        // 在这个路径段中\n        const segmentProgress = (targetDistance - currentDistance) / segmentDistance;\n        this.position.lerpVectors(segmentStart, segmentEnd, segmentProgress);\n\n        // 设置朝向\n        const direction = new THREE.Vector3().subVectors(segmentEnd, segmentStart).normalize();\n        if (direction.length() > 0) {\n          const angle = Math.atan2(direction.x, direction.z);\n          this.mesh.rotation.y = angle;\n        }\n        break;\n      }\n      currentDistance += segmentDistance;\n    }\n\n    // 更新路径进度\n    this.pathProgress += this.moveSpeed * deltaTime / totalDistance;\n    if (this.pathProgress >= 1) {\n      this.pathProgress = 1;\n      this.stopMoving();\n    }\n  }\n  calculatePathDistance() {\n    if (!this.currentPath || this.currentPath.length < 2) return 0;\n    let totalDistance = 0;\n    for (let i = 0; i < this.currentPath.length - 1; i++) {\n      totalDistance += this.currentPath[i].distanceTo(this.currentPath[i + 1]);\n    }\n    return totalDistance;\n  }\n  startMoving(path) {\n    this.currentPath = path.map(point => point.clone());\n    this.pathProgress = 0;\n    this.isMoving = true;\n  }\n  stopMoving() {\n    this.isMoving = false;\n    this.currentPath = null;\n    this.pathProgress = 0;\n\n    // 重置动画状态\n    if (this.bodyParts) {\n      this.bodyParts.leftArm.rotation.x = 0;\n      this.bodyParts.rightArm.rotation.x = 0;\n      this.bodyParts.leftLeg.rotation.x = 0;\n      this.bodyParts.rightLeg.rotation.x = 0;\n      this.bodyParts.body.position.y = 0.6;\n    }\n  }\n  setSelected(selected) {\n    this.isSelected = selected;\n    if (this.mesh) {\n      this.mesh.children.forEach(child => {\n        if (child.material) {\n          if (selected) {\n            child.material.emissive.setHex(0x444444);\n          } else {\n            child.material.emissive.setHex(0x000000);\n          }\n        }\n      });\n    }\n  }\n  updateProperty(property, value) {\n    switch (property) {\n      case 'name':\n        this.name = value;\n        break;\n      case 'moveSpeed':\n        this.moveSpeed = Math.max(0.1, Math.min(10, value));\n        break;\n    }\n  }\n\n  // 获取边界框（用于碰撞检测）\n  getBoundingBox() {\n    const box = new THREE.Box3();\n    if (this.mesh) {\n      box.setFromObject(this.mesh);\n    }\n    return box;\n  }\n\n  // 销毁对象\n  dispose() {\n    if (this.mesh) {\n      this.mesh.children.forEach(child => {\n        if (child.geometry) child.geometry.dispose();\n        if (child.material) child.material.dispose();\n      });\n    }\n  }\n}", "map": {"version": 3, "names": ["THREE", "Person", "constructor", "id", "name", "position", "Vector3", "type", "clone", "isSelected", "<PERSON><PERSON><PERSON>", "setupAnimation", "group", "Group", "bodyGeometry", "CylinderGeometry", "bodyMaterial", "MeshLambertMaterial", "color", "body", "<PERSON><PERSON>", "y", "<PERSON><PERSON><PERSON><PERSON>", "add", "headGeometry", "SphereGeometry", "headMaterial", "head", "armGeometry", "armMaterial", "leftArm", "set", "rotation", "z", "Math", "PI", "rightArm", "legGeometry", "legMaterial", "leftLeg", "rightLeg", "copy", "userData", "draggable", "objectType", "objectId", "mesh", "bodyParts", "animationTime", "walkCycle", "armSwing", "legSwing", "update", "deltaTime", "isMoving", "updateWalkAnimation", "updatePathMovement", "walkSpeed", "armSwingAmount", "legSwingAmount", "x", "sin", "currentPath", "length", "stopMoving", "totalDistance", "calculatePathDistance", "targetDistance", "pathProgress", "currentDistance", "i", "segmentStart", "segmentEnd", "segmentDistance", "distanceTo", "segmentProgress", "lerpVectors", "direction", "subVectors", "normalize", "angle", "atan2", "moveSpeed", "startMoving", "path", "map", "point", "setSelected", "selected", "children", "for<PERSON>ach", "child", "material", "emissive", "setHex", "updateProperty", "property", "value", "max", "min", "getBoundingBox", "box", "Box3", "setFromObject", "dispose", "geometry"], "sources": ["D:/code/map/my-map/src/classes/Person.js"], "sourcesContent": ["import * as THREE from 'three'\n\nexport class Person {\n  constructor(id, name, position = new THREE.Vector3(0, 0, 0)) {\n    this.id = id\n    this.name = name\n    this.type = 'person'\n    this.position = position.clone()\n    this.isSelected = false\n    \n    this.createMesh()\n    this.setupAnimation()\n  }\n  \n  createMesh() {\n    // 创建人物的3D模型（简化版，使用基本几何体）\n    const group = new THREE.Group()\n\n    // 身体（圆柱体）\n    const bodyGeometry = new THREE.CylinderGeometry(0.3, 0.4, 1.2, 8)\n    const bodyMaterial = new THREE.MeshLambertMaterial({ color: 0x4a90e2 })\n    const body = new THREE.Mesh(bodyGeometry, bodyMaterial)\n    body.position.y = 0.6\n    body.castShadow = true\n    group.add(body)\n    \n    // 头部（球体）\n    const headGeometry = new THREE.SphereGeometry(0.25, 8, 6)\n    const headMaterial = new THREE.MeshLambertMaterial({ color: 0xffdbac })\n    const head = new THREE.Mesh(headGeometry, headMaterial)\n    head.position.y = 1.45\n    head.castShadow = true\n    group.add(head)\n    \n    // 左臂\n    const armGeometry = new THREE.CylinderGeometry(0.08, 0.08, 0.8, 6)\n    const armMaterial = new THREE.MeshLambertMaterial({ color: 0xffdbac })\n    \n    const leftArm = new THREE.Mesh(armGeometry, armMaterial)\n    leftArm.position.set(-0.45, 0.8, 0)\n    leftArm.rotation.z = Math.PI / 6\n    leftArm.castShadow = true\n    group.add(leftArm)\n    \n    const rightArm = new THREE.Mesh(armGeometry, armMaterial)\n    rightArm.position.set(0.45, 0.8, 0)\n    rightArm.rotation.z = -Math.PI / 6\n    rightArm.castShadow = true\n    group.add(rightArm)\n    \n    // 左腿\n    const legGeometry = new THREE.CylinderGeometry(0.1, 0.1, 0.8, 6)\n    const legMaterial = new THREE.MeshLambertMaterial({ color: 0x2c3e50 })\n    \n    const leftLeg = new THREE.Mesh(legGeometry, legMaterial)\n    leftLeg.position.set(-0.15, -0.4, 0)\n    leftLeg.castShadow = true\n    group.add(leftLeg)\n    \n    const rightLeg = new THREE.Mesh(legGeometry, legMaterial)\n    rightLeg.position.set(0.15, -0.4, 0)\n    rightLeg.castShadow = true\n    group.add(rightLeg)\n    \n    // 设置位置\n    group.position.copy(this.position)\n    \n    // 添加用户数据\n    group.userData = {\n      draggable: true,\n      objectType: 'person',\n      objectId: this.id\n    }\n    \n    this.mesh = group\n    this.bodyParts = {\n      body,\n      head,\n      leftArm,\n      rightArm,\n      leftLeg,\n      rightLeg\n    }\n  }\n  \n  setupAnimation() {\n    this.animationTime = 0\n    this.walkCycle = {\n      armSwing: 0,\n      legSwing: 0\n    }\n  }\n  \n  update(deltaTime = 0.016) {\n    this.animationTime += deltaTime\n    \n    // 如果正在移动，播放走路动画\n    if (this.isMoving) {\n      this.updateWalkAnimation()\n      this.updatePathMovement(deltaTime)\n    }\n    \n    // 更新网格位置\n    if (this.mesh) {\n      this.mesh.position.copy(this.position)\n    }\n  }\n  \n  updateWalkAnimation() {\n    const walkSpeed = 4\n    const armSwingAmount = 0.3\n    const legSwingAmount = 0.2\n    \n    // 手臂摆动\n    this.bodyParts.leftArm.rotation.x = Math.sin(this.animationTime * walkSpeed) * armSwingAmount\n    this.bodyParts.rightArm.rotation.x = -Math.sin(this.animationTime * walkSpeed) * armSwingAmount\n    \n    // 腿部摆动\n    this.bodyParts.leftLeg.rotation.x = Math.sin(this.animationTime * walkSpeed) * legSwingAmount\n    this.bodyParts.rightLeg.rotation.x = -Math.sin(this.animationTime * walkSpeed) * legSwingAmount\n    \n    // 身体轻微上下摆动\n    this.bodyParts.body.position.y = 0.6 + Math.sin(this.animationTime * walkSpeed * 2) * 0.05\n  }\n  \n  updatePathMovement(deltaTime) {\n    if (!this.currentPath || this.currentPath.length < 2) {\n      this.stopMoving()\n      return\n    }\n    \n    const totalDistance = this.calculatePathDistance()\n    const targetDistance = this.pathProgress * totalDistance\n    let currentDistance = 0\n    \n    // 找到当前应该在的路径段\n    for (let i = 0; i < this.currentPath.length - 1; i++) {\n      const segmentStart = this.currentPath[i]\n      const segmentEnd = this.currentPath[i + 1]\n      const segmentDistance = segmentStart.distanceTo(segmentEnd)\n      \n      if (currentDistance + segmentDistance >= targetDistance) {\n        // 在这个路径段中\n        const segmentProgress = (targetDistance - currentDistance) / segmentDistance\n        this.position.lerpVectors(segmentStart, segmentEnd, segmentProgress)\n        \n        // 设置朝向\n        const direction = new THREE.Vector3().subVectors(segmentEnd, segmentStart).normalize()\n        if (direction.length() > 0) {\n          const angle = Math.atan2(direction.x, direction.z)\n          this.mesh.rotation.y = angle\n        }\n        \n        break\n      }\n      currentDistance += segmentDistance\n    }\n    \n    // 更新路径进度\n    this.pathProgress += (this.moveSpeed * deltaTime) / totalDistance\n    \n    if (this.pathProgress >= 1) {\n      this.pathProgress = 1\n      this.stopMoving()\n    }\n  }\n  \n  calculatePathDistance() {\n    if (!this.currentPath || this.currentPath.length < 2) return 0\n    \n    let totalDistance = 0\n    for (let i = 0; i < this.currentPath.length - 1; i++) {\n      totalDistance += this.currentPath[i].distanceTo(this.currentPath[i + 1])\n    }\n    return totalDistance\n  }\n  \n  startMoving(path) {\n    this.currentPath = path.map(point => point.clone())\n    this.pathProgress = 0\n    this.isMoving = true\n  }\n  \n  stopMoving() {\n    this.isMoving = false\n    this.currentPath = null\n    this.pathProgress = 0\n    \n    // 重置动画状态\n    if (this.bodyParts) {\n      this.bodyParts.leftArm.rotation.x = 0\n      this.bodyParts.rightArm.rotation.x = 0\n      this.bodyParts.leftLeg.rotation.x = 0\n      this.bodyParts.rightLeg.rotation.x = 0\n      this.bodyParts.body.position.y = 0.6\n    }\n  }\n  \n  setSelected(selected) {\n    this.isSelected = selected\n    \n    if (this.mesh) {\n      this.mesh.children.forEach(child => {\n        if (child.material) {\n          if (selected) {\n            child.material.emissive.setHex(0x444444)\n          } else {\n            child.material.emissive.setHex(0x000000)\n          }\n        }\n      })\n    }\n  }\n  \n  updateProperty(property, value) {\n    switch (property) {\n      case 'name':\n        this.name = value\n        break\n      case 'moveSpeed':\n        this.moveSpeed = Math.max(0.1, Math.min(10, value))\n        break\n    }\n  }\n  \n  // 获取边界框（用于碰撞检测）\n  getBoundingBox() {\n    const box = new THREE.Box3()\n    if (this.mesh) {\n      box.setFromObject(this.mesh)\n    }\n    return box\n  }\n  \n  // 销毁对象\n  dispose() {\n    if (this.mesh) {\n      this.mesh.children.forEach(child => {\n        if (child.geometry) child.geometry.dispose()\n        if (child.material) child.material.dispose()\n      })\n    }\n  }\n}\n"], "mappings": ";;;AAAA,OAAO,KAAKA,KAAK,MAAM,OAAO;AAE9B,OAAO,MAAMC,MAAM,CAAC;EAClBC,WAAWA,CAACC,EAAE,EAAEC,IAAI,EAAEC,QAAQ,GAAG,IAAIL,KAAK,CAACM,OAAO,CAAC,CAAC,EAAE,CAAC,EAAE,CAAC,CAAC,EAAE;IAC3D,IAAI,CAACH,EAAE,GAAGA,EAAE;IACZ,IAAI,CAACC,IAAI,GAAGA,IAAI;IAChB,IAAI,CAACG,IAAI,GAAG,QAAQ;IACpB,IAAI,CAACF,QAAQ,GAAGA,QAAQ,CAACG,KAAK,CAAC,CAAC;IAChC,IAAI,CAACC,UAAU,GAAG,KAAK;IAEvB,IAAI,CAACC,UAAU,CAAC,CAAC;IACjB,IAAI,CAACC,cAAc,CAAC,CAAC;EACvB;EAEAD,UAAUA,CAAA,EAAG;IACX;IACA,MAAME,KAAK,GAAG,IAAIZ,KAAK,CAACa,KAAK,CAAC,CAAC;;IAE/B;IACA,MAAMC,YAAY,GAAG,IAAId,KAAK,CAACe,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IACjE,MAAMC,YAAY,GAAG,IAAIhB,KAAK,CAACiB,mBAAmB,CAAC;MAAEC,KAAK,EAAE;IAAS,CAAC,CAAC;IACvE,MAAMC,IAAI,GAAG,IAAInB,KAAK,CAACoB,IAAI,CAACN,YAAY,EAAEE,YAAY,CAAC;IACvDG,IAAI,CAACd,QAAQ,CAACgB,CAAC,GAAG,GAAG;IACrBF,IAAI,CAACG,UAAU,GAAG,IAAI;IACtBV,KAAK,CAACW,GAAG,CAACJ,IAAI,CAAC;;IAEf;IACA,MAAMK,YAAY,GAAG,IAAIxB,KAAK,CAACyB,cAAc,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,CAAC;IACzD,MAAMC,YAAY,GAAG,IAAI1B,KAAK,CAACiB,mBAAmB,CAAC;MAAEC,KAAK,EAAE;IAAS,CAAC,CAAC;IACvE,MAAMS,IAAI,GAAG,IAAI3B,KAAK,CAACoB,IAAI,CAACI,YAAY,EAAEE,YAAY,CAAC;IACvDC,IAAI,CAACtB,QAAQ,CAACgB,CAAC,GAAG,IAAI;IACtBM,IAAI,CAACL,UAAU,GAAG,IAAI;IACtBV,KAAK,CAACW,GAAG,CAACI,IAAI,CAAC;;IAEf;IACA,MAAMC,WAAW,GAAG,IAAI5B,KAAK,CAACe,gBAAgB,CAAC,IAAI,EAAE,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;IAClE,MAAMc,WAAW,GAAG,IAAI7B,KAAK,CAACiB,mBAAmB,CAAC;MAAEC,KAAK,EAAE;IAAS,CAAC,CAAC;IAEtE,MAAMY,OAAO,GAAG,IAAI9B,KAAK,CAACoB,IAAI,CAACQ,WAAW,EAAEC,WAAW,CAAC;IACxDC,OAAO,CAACzB,QAAQ,CAAC0B,GAAG,CAAC,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;IACnCD,OAAO,CAACE,QAAQ,CAACC,CAAC,GAAGC,IAAI,CAACC,EAAE,GAAG,CAAC;IAChCL,OAAO,CAACR,UAAU,GAAG,IAAI;IACzBV,KAAK,CAACW,GAAG,CAACO,OAAO,CAAC;IAElB,MAAMM,QAAQ,GAAG,IAAIpC,KAAK,CAACoB,IAAI,CAACQ,WAAW,EAAEC,WAAW,CAAC;IACzDO,QAAQ,CAAC/B,QAAQ,CAAC0B,GAAG,CAAC,IAAI,EAAE,GAAG,EAAE,CAAC,CAAC;IACnCK,QAAQ,CAACJ,QAAQ,CAACC,CAAC,GAAG,CAACC,IAAI,CAACC,EAAE,GAAG,CAAC;IAClCC,QAAQ,CAACd,UAAU,GAAG,IAAI;IAC1BV,KAAK,CAACW,GAAG,CAACa,QAAQ,CAAC;;IAEnB;IACA,MAAMC,WAAW,GAAG,IAAIrC,KAAK,CAACe,gBAAgB,CAAC,GAAG,EAAE,GAAG,EAAE,GAAG,EAAE,CAAC,CAAC;IAChE,MAAMuB,WAAW,GAAG,IAAItC,KAAK,CAACiB,mBAAmB,CAAC;MAAEC,KAAK,EAAE;IAAS,CAAC,CAAC;IAEtE,MAAMqB,OAAO,GAAG,IAAIvC,KAAK,CAACoB,IAAI,CAACiB,WAAW,EAAEC,WAAW,CAAC;IACxDC,OAAO,CAAClC,QAAQ,CAAC0B,GAAG,CAAC,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IACpCQ,OAAO,CAACjB,UAAU,GAAG,IAAI;IACzBV,KAAK,CAACW,GAAG,CAACgB,OAAO,CAAC;IAElB,MAAMC,QAAQ,GAAG,IAAIxC,KAAK,CAACoB,IAAI,CAACiB,WAAW,EAAEC,WAAW,CAAC;IACzDE,QAAQ,CAACnC,QAAQ,CAAC0B,GAAG,CAAC,IAAI,EAAE,CAAC,GAAG,EAAE,CAAC,CAAC;IACpCS,QAAQ,CAAClB,UAAU,GAAG,IAAI;IAC1BV,KAAK,CAACW,GAAG,CAACiB,QAAQ,CAAC;;IAEnB;IACA5B,KAAK,CAACP,QAAQ,CAACoC,IAAI,CAAC,IAAI,CAACpC,QAAQ,CAAC;;IAElC;IACAO,KAAK,CAAC8B,QAAQ,GAAG;MACfC,SAAS,EAAE,IAAI;MACfC,UAAU,EAAE,QAAQ;MACpBC,QAAQ,EAAE,IAAI,CAAC1C;IACjB,CAAC;IAED,IAAI,CAAC2C,IAAI,GAAGlC,KAAK;IACjB,IAAI,CAACmC,SAAS,GAAG;MACf5B,IAAI;MACJQ,IAAI;MACJG,OAAO;MACPM,QAAQ;MACRG,OAAO;MACPC;IACF,CAAC;EACH;EAEA7B,cAAcA,CAAA,EAAG;IACf,IAAI,CAACqC,aAAa,GAAG,CAAC;IACtB,IAAI,CAACC,SAAS,GAAG;MACfC,QAAQ,EAAE,CAAC;MACXC,QAAQ,EAAE;IACZ,CAAC;EACH;EAEAC,MAAMA,CAACC,SAAS,GAAG,KAAK,EAAE;IACxB,IAAI,CAACL,aAAa,IAAIK,SAAS;;IAE/B;IACA,IAAI,IAAI,CAACC,QAAQ,EAAE;MACjB,IAAI,CAACC,mBAAmB,CAAC,CAAC;MAC1B,IAAI,CAACC,kBAAkB,CAACH,SAAS,CAAC;IACpC;;IAEA;IACA,IAAI,IAAI,CAACP,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAACzC,QAAQ,CAACoC,IAAI,CAAC,IAAI,CAACpC,QAAQ,CAAC;IACxC;EACF;EAEAkD,mBAAmBA,CAAA,EAAG;IACpB,MAAME,SAAS,GAAG,CAAC;IACnB,MAAMC,cAAc,GAAG,GAAG;IAC1B,MAAMC,cAAc,GAAG,GAAG;;IAE1B;IACA,IAAI,CAACZ,SAAS,CAACjB,OAAO,CAACE,QAAQ,CAAC4B,CAAC,GAAG1B,IAAI,CAAC2B,GAAG,CAAC,IAAI,CAACb,aAAa,GAAGS,SAAS,CAAC,GAAGC,cAAc;IAC7F,IAAI,CAACX,SAAS,CAACX,QAAQ,CAACJ,QAAQ,CAAC4B,CAAC,GAAG,CAAC1B,IAAI,CAAC2B,GAAG,CAAC,IAAI,CAACb,aAAa,GAAGS,SAAS,CAAC,GAAGC,cAAc;;IAE/F;IACA,IAAI,CAACX,SAAS,CAACR,OAAO,CAACP,QAAQ,CAAC4B,CAAC,GAAG1B,IAAI,CAAC2B,GAAG,CAAC,IAAI,CAACb,aAAa,GAAGS,SAAS,CAAC,GAAGE,cAAc;IAC7F,IAAI,CAACZ,SAAS,CAACP,QAAQ,CAACR,QAAQ,CAAC4B,CAAC,GAAG,CAAC1B,IAAI,CAAC2B,GAAG,CAAC,IAAI,CAACb,aAAa,GAAGS,SAAS,CAAC,GAAGE,cAAc;;IAE/F;IACA,IAAI,CAACZ,SAAS,CAAC5B,IAAI,CAACd,QAAQ,CAACgB,CAAC,GAAG,GAAG,GAAGa,IAAI,CAAC2B,GAAG,CAAC,IAAI,CAACb,aAAa,GAAGS,SAAS,GAAG,CAAC,CAAC,GAAG,IAAI;EAC5F;EAEAD,kBAAkBA,CAACH,SAAS,EAAE;IAC5B,IAAI,CAAC,IAAI,CAACS,WAAW,IAAI,IAAI,CAACA,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE;MACpD,IAAI,CAACC,UAAU,CAAC,CAAC;MACjB;IACF;IAEA,MAAMC,aAAa,GAAG,IAAI,CAACC,qBAAqB,CAAC,CAAC;IAClD,MAAMC,cAAc,GAAG,IAAI,CAACC,YAAY,GAAGH,aAAa;IACxD,IAAII,eAAe,GAAG,CAAC;;IAEvB;IACA,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACR,WAAW,CAACC,MAAM,GAAG,CAAC,EAAEO,CAAC,EAAE,EAAE;MACpD,MAAMC,YAAY,GAAG,IAAI,CAACT,WAAW,CAACQ,CAAC,CAAC;MACxC,MAAME,UAAU,GAAG,IAAI,CAACV,WAAW,CAACQ,CAAC,GAAG,CAAC,CAAC;MAC1C,MAAMG,eAAe,GAAGF,YAAY,CAACG,UAAU,CAACF,UAAU,CAAC;MAE3D,IAAIH,eAAe,GAAGI,eAAe,IAAIN,cAAc,EAAE;QACvD;QACA,MAAMQ,eAAe,GAAG,CAACR,cAAc,GAAGE,eAAe,IAAII,eAAe;QAC5E,IAAI,CAACpE,QAAQ,CAACuE,WAAW,CAACL,YAAY,EAAEC,UAAU,EAAEG,eAAe,CAAC;;QAEpE;QACA,MAAME,SAAS,GAAG,IAAI7E,KAAK,CAACM,OAAO,CAAC,CAAC,CAACwE,UAAU,CAACN,UAAU,EAAED,YAAY,CAAC,CAACQ,SAAS,CAAC,CAAC;QACtF,IAAIF,SAAS,CAACd,MAAM,CAAC,CAAC,GAAG,CAAC,EAAE;UAC1B,MAAMiB,KAAK,GAAG9C,IAAI,CAAC+C,KAAK,CAACJ,SAAS,CAACjB,CAAC,EAAEiB,SAAS,CAAC5C,CAAC,CAAC;UAClD,IAAI,CAACa,IAAI,CAACd,QAAQ,CAACX,CAAC,GAAG2D,KAAK;QAC9B;QAEA;MACF;MACAX,eAAe,IAAII,eAAe;IACpC;;IAEA;IACA,IAAI,CAACL,YAAY,IAAK,IAAI,CAACc,SAAS,GAAG7B,SAAS,GAAIY,aAAa;IAEjE,IAAI,IAAI,CAACG,YAAY,IAAI,CAAC,EAAE;MAC1B,IAAI,CAACA,YAAY,GAAG,CAAC;MACrB,IAAI,CAACJ,UAAU,CAAC,CAAC;IACnB;EACF;EAEAE,qBAAqBA,CAAA,EAAG;IACtB,IAAI,CAAC,IAAI,CAACJ,WAAW,IAAI,IAAI,CAACA,WAAW,CAACC,MAAM,GAAG,CAAC,EAAE,OAAO,CAAC;IAE9D,IAAIE,aAAa,GAAG,CAAC;IACrB,KAAK,IAAIK,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAG,IAAI,CAACR,WAAW,CAACC,MAAM,GAAG,CAAC,EAAEO,CAAC,EAAE,EAAE;MACpDL,aAAa,IAAI,IAAI,CAACH,WAAW,CAACQ,CAAC,CAAC,CAACI,UAAU,CAAC,IAAI,CAACZ,WAAW,CAACQ,CAAC,GAAG,CAAC,CAAC,CAAC;IAC1E;IACA,OAAOL,aAAa;EACtB;EAEAkB,WAAWA,CAACC,IAAI,EAAE;IAChB,IAAI,CAACtB,WAAW,GAAGsB,IAAI,CAACC,GAAG,CAACC,KAAK,IAAIA,KAAK,CAAC9E,KAAK,CAAC,CAAC,CAAC;IACnD,IAAI,CAAC4D,YAAY,GAAG,CAAC;IACrB,IAAI,CAACd,QAAQ,GAAG,IAAI;EACtB;EAEAU,UAAUA,CAAA,EAAG;IACX,IAAI,CAACV,QAAQ,GAAG,KAAK;IACrB,IAAI,CAACQ,WAAW,GAAG,IAAI;IACvB,IAAI,CAACM,YAAY,GAAG,CAAC;;IAErB;IACA,IAAI,IAAI,CAACrB,SAAS,EAAE;MAClB,IAAI,CAACA,SAAS,CAACjB,OAAO,CAACE,QAAQ,CAAC4B,CAAC,GAAG,CAAC;MACrC,IAAI,CAACb,SAAS,CAACX,QAAQ,CAACJ,QAAQ,CAAC4B,CAAC,GAAG,CAAC;MACtC,IAAI,CAACb,SAAS,CAACR,OAAO,CAACP,QAAQ,CAAC4B,CAAC,GAAG,CAAC;MACrC,IAAI,CAACb,SAAS,CAACP,QAAQ,CAACR,QAAQ,CAAC4B,CAAC,GAAG,CAAC;MACtC,IAAI,CAACb,SAAS,CAAC5B,IAAI,CAACd,QAAQ,CAACgB,CAAC,GAAG,GAAG;IACtC;EACF;EAEAkE,WAAWA,CAACC,QAAQ,EAAE;IACpB,IAAI,CAAC/E,UAAU,GAAG+E,QAAQ;IAE1B,IAAI,IAAI,CAAC1C,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAAC2C,QAAQ,CAACC,OAAO,CAACC,KAAK,IAAI;QAClC,IAAIA,KAAK,CAACC,QAAQ,EAAE;UAClB,IAAIJ,QAAQ,EAAE;YACZG,KAAK,CAACC,QAAQ,CAACC,QAAQ,CAACC,MAAM,CAAC,QAAQ,CAAC;UAC1C,CAAC,MAAM;YACLH,KAAK,CAACC,QAAQ,CAACC,QAAQ,CAACC,MAAM,CAAC,QAAQ,CAAC;UAC1C;QACF;MACF,CAAC,CAAC;IACJ;EACF;EAEAC,cAAcA,CAACC,QAAQ,EAAEC,KAAK,EAAE;IAC9B,QAAQD,QAAQ;MACd,KAAK,MAAM;QACT,IAAI,CAAC5F,IAAI,GAAG6F,KAAK;QACjB;MACF,KAAK,WAAW;QACd,IAAI,CAACf,SAAS,GAAGhD,IAAI,CAACgE,GAAG,CAAC,GAAG,EAAEhE,IAAI,CAACiE,GAAG,CAAC,EAAE,EAAEF,KAAK,CAAC,CAAC;QACnD;IACJ;EACF;;EAEA;EACAG,cAAcA,CAAA,EAAG;IACf,MAAMC,GAAG,GAAG,IAAIrG,KAAK,CAACsG,IAAI,CAAC,CAAC;IAC5B,IAAI,IAAI,CAACxD,IAAI,EAAE;MACbuD,GAAG,CAACE,aAAa,CAAC,IAAI,CAACzD,IAAI,CAAC;IAC9B;IACA,OAAOuD,GAAG;EACZ;;EAEA;EACAG,OAAOA,CAAA,EAAG;IACR,IAAI,IAAI,CAAC1D,IAAI,EAAE;MACb,IAAI,CAACA,IAAI,CAAC2C,QAAQ,CAACC,OAAO,CAACC,KAAK,IAAI;QAClC,IAAIA,KAAK,CAACc,QAAQ,EAAEd,KAAK,CAACc,QAAQ,CAACD,OAAO,CAAC,CAAC;QAC5C,IAAIb,KAAK,CAACC,QAAQ,EAAED,KAAK,CAACC,QAAQ,CAACY,OAAO,CAAC,CAAC;MAC9C,CAAC,CAAC;IACJ;EACF;AACF", "ignoreList": []}, "metadata": {}, "sourceType": "module", "externalDependencies": []}